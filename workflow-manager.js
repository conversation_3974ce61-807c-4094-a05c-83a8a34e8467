const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');

// 引入相关模块
const { AIOutlineGenerator } = require('./ai-outline-generator');
const { AutoWriter } = require('./auto-writer');
const { ForeshadowingManager } = require('./foreshadowing-manager');

/**
 * 全自动化创作流程管理器
 * 管理从大纲生成到分卷创作的完整流程
 */
class WorkflowManager {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.workflowStatePath = path.join(projectPath, '6_automation', 'workflow_state.yml');
    this.outlineGenerator = new AIOutlineGenerator(projectPath);
    this.autoWriter = new AutoWriter(projectPath);
    this.foreshadowingManager = new ForeshadowingManager(projectPath);
  }

  /**
   * 初始化工作流程管理器
   */
  async initialize() {
    await this.ensureDirectoryExists(path.dirname(this.workflowStatePath));
    await this.foreshadowingManager.initialize();
    await this.autoWriter.initialize();
    
    // 初始化工作流程状态
    if (!await this.fileExists(this.workflowStatePath)) {
      const initialState = {
        workflow_id: this.generateWorkflowId(),
        created_at: new Date().toISOString(),
        current_phase: 'initial',
        phases: {
          initial: { status: 'pending', started_at: null, completed_at: null },
          main_outline: { status: 'pending', started_at: null, completed_at: null, editing_allowed: true },
          volume_outlines: { status: 'pending', started_at: null, completed_at: null, editing_allowed: true },
          volume_writing: { status: 'pending', started_at: null, completed_at: null }
        },
        user_preferences: {
          auto_proceed_after_generation: false,
          require_confirmation_before_writing: true,
          chapters_per_batch: 5,
          pause_between_volumes: true
        },
        volumes: {},
        progress: {
          total_volumes: 0,
          completed_volumes: 0,
          current_volume: null,
          total_chapters: 0,
          completed_chapters: 0
        },
        interruption_points: []
      };
      
      await this.saveWorkflowState(initialState);
    }
  }

  /**
   * 启动完整的创作工作流程
   */
  async startCompleteWorkflow(projectConfig = {}) {
    try {
      console.log('🚀 启动完整创作工作流程...');
      
      const state = await this.loadWorkflowState();
      state.started_at = new Date().toISOString();
      state.project_config = projectConfig;
      
      await this.logWorkflowEvent('workflow_started', { config: projectConfig });

      // 阶段1: 生成主线大纲
      if (state.current_phase === 'initial' || state.current_phase === 'main_outline') {
        const outlineResult = await this.executeMainOutlinePhase(projectConfig);
        if (!outlineResult.success) {
          return this.handleError('主线大纲生成失败', outlineResult.error);
        }
        
        // 更新状态并等待用户确认
        state.current_phase = 'main_outline_review';
        state.phases.main_outline.status = 'completed';
        state.phases.main_outline.completed_at = new Date().toISOString();
        await this.saveWorkflowState(state);
        
        return {
          success: true,
          phase: 'main_outline_review',
          message: '主线大纲已生成，请审阅和编辑',
          data: outlineResult,
          next_action: 'review_main_outline'
        };
      }

      return { success: false, error: '无效的工作流程状态' };

    } catch (error) {
      console.error('启动创作工作流程失败:', error);
      await this.logWorkflowEvent('workflow_error', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  /**
   * 确认主线大纲并继续流程
   */
  async confirmMainOutline(userEdits = null) {
    try {
      console.log('📋 确认主线大纲，继续下一阶段...');
      
      const state = await this.loadWorkflowState();
      
      // 如果用户提供了编辑内容，保存编辑
      if (userEdits) {
        await this.applyMainOutlineEdits(userEdits);
        await this.logWorkflowEvent('main_outline_edited', { edits: Object.keys(userEdits) });
      }

      // 阶段2: 生成分卷细纲
      const volumeOutlineResult = await this.executeVolumeOutlinesPhase();
      if (!volumeOutlineResult.success) {
        return this.handleError('分卷细纲生成失败', volumeOutlineResult.error);
      }

      // 更新状态
      state.current_phase = 'volume_outlines_review';
      state.phases.volume_outlines.status = 'completed';
      state.phases.volume_outlines.completed_at = new Date().toISOString();
      state.progress.total_volumes = volumeOutlineResult.volumes.length;
      
      // 初始化分卷状态
      volumeOutlineResult.volumes.forEach(volume => {
        state.volumes[volume.id] = {
          id: volume.id,
          title: volume.title,
          status: 'outline_generated',
          chapters_total: volume.estimated_chapters || 25,
          chapters_completed: 0,
          writing_started: false,
          writing_completed: false
        };
      });

      await this.saveWorkflowState(state);

      return {
        success: true,
        phase: 'volume_outlines_review',
        message: '分卷细纲已生成，请审阅和编辑',
        data: volumeOutlineResult,
        next_action: 'review_volume_outlines'
      };

    } catch (error) {
      console.error('确认主线大纲失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 确认分卷细纲并准备创作
   */
  async confirmVolumeOutlines(userEdits = null) {
    try {
      console.log('📚 确认分卷细纲，准备开始创作...');
      
      const state = await this.loadWorkflowState();
      
      // 如果用户提供了编辑内容，保存编辑
      if (userEdits) {
        await this.applyVolumeOutlineEdits(userEdits);
        await this.logWorkflowEvent('volume_outlines_edited', { 
          edited_volumes: Object.keys(userEdits) 
        });
      }

      // 更新状态，准备开始创作阶段
      state.current_phase = 'volume_writing_ready';
      state.phases.volume_writing.status = 'ready';
      await this.saveWorkflowState(state);

      return {
        success: true,
        phase: 'volume_writing_ready',
        message: '分卷细纲确认完成，可以开始分卷创作',
        volumes: Object.values(state.volumes),
        next_action: 'select_volume_to_write'
      };

    } catch (error) {
      console.error('确认分卷细纲失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 开始指定分卷的创作
   */
  async startVolumeWriting(volumeId, writingConfig = {}) {
    try {
      console.log(`📖 开始第${volumeId}卷的创作...`);
      
      const state = await this.loadWorkflowState();
      
      if (!state.volumes[volumeId]) {
        return { success: false, error: `分卷 ${volumeId} 不存在` };
      }

      if (state.volumes[volumeId].writing_completed) {
        return { success: false, error: `分卷 ${volumeId} 已经完成创作` };
      }

      // 更新分卷状态
      state.volumes[volumeId].status = 'writing_in_progress';
      state.volumes[volumeId].writing_started = true;
      state.volumes[volumeId].writing_started_at = new Date().toISOString();
      state.progress.current_volume = volumeId;
      state.current_phase = 'volume_writing';

      await this.saveWorkflowState(state);
      await this.logWorkflowEvent('volume_writing_started', { volume_id: volumeId, config: writingConfig });

      // 启动分卷创作
      const writingResult = await this.executeVolumeWriting(volumeId, writingConfig);
      
      if (writingResult.success) {
        // 更新完成状态
        state.volumes[volumeId].status = 'completed';
        state.volumes[volumeId].writing_completed = true;
        state.volumes[volumeId].writing_completed_at = new Date().toISOString();
        state.volumes[volumeId].chapters_completed = writingResult.chapters_completed;
        state.progress.completed_volumes++;
        state.progress.completed_chapters += writingResult.chapters_completed;

        // 检查是否所有分卷都完成
        const allVolumesCompleted = Object.values(state.volumes).every(v => v.writing_completed);
        if (allVolumesCompleted) {
          state.current_phase = 'completed';
          state.completed_at = new Date().toISOString();
        } else {
          state.current_phase = 'volume_writing_ready';
          state.progress.current_volume = null;
        }

        await this.saveWorkflowState(state);
        await this.logWorkflowEvent('volume_writing_completed', { 
          volume_id: volumeId, 
          chapters_created: writingResult.chapters_completed 
        });
      }

      return writingResult;

    } catch (error) {
      console.error(`分卷${volumeId}创作失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 执行主线大纲生成阶段
   */
  async executeMainOutlinePhase(projectConfig) {
    try {
      console.log('📋 执行主线大纲生成阶段...');
      
      const state = await this.loadWorkflowState();
      state.phases.main_outline.status = 'in_progress';
      state.phases.main_outline.started_at = new Date().toISOString();
      await this.saveWorkflowState(state);

      // 构建大纲生成要求
      const outlineRequirements = {
        genre: projectConfig.genre || '网络小说',
        theme: projectConfig.theme || '',
        style: projectConfig.style || '现代网文风格',
        special_requirements: projectConfig.special_requirements || '',
        ...projectConfig.outline_requirements
      };

      const result = await this.outlineGenerator.generateMainOutline(outlineRequirements);
      
      if (result.success) {
        await this.logWorkflowEvent('main_outline_generated', {
          outline_path: result.outlinePath,
          volumes: result.volumes
        });
      }

      return result;

    } catch (error) {
      console.error('主线大纲生成阶段失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 执行分卷细纲生成阶段
   */
  async executeVolumeOutlinesPhase() {
    try {
      console.log('📚 执行分卷细纲生成阶段...');
      
      const state = await this.loadWorkflowState();
      state.phases.volume_outlines.status = 'in_progress';
      state.phases.volume_outlines.started_at = new Date().toISOString();
      await this.saveWorkflowState(state);

      // 解析主线大纲获取分卷信息
      const mainOutlinePath = path.join(this.projectPath, '2_plot', 'main_outline.md');
      const mainOutlineContent = await fs.readFile(mainOutlinePath, 'utf8');
      const volumes = await this.parseVolumesFromMainOutline(mainOutlineContent);

      const volumeResults = [];
      for (const volume of volumes) {
        console.log(`生成第${volume.id}卷细纲...`);
        
        const volumeGuidance = {
          volume_title: volume.title,
          target_chapters: volume.estimated_chapters || 25,
          ...(state.project_config?.volume_guidance || {})
        };

        const volumeResult = await this.outlineGenerator.generateVolumeOutline(volume.id, volumeGuidance);
        
        if (volumeResult.success) {
          volumeResults.push({
            id: volume.id,
            title: volume.title,
            outline_path: volumeResult.volumeOutlinePath,
            chapters: volumeResult.chapters,
            estimated_chapters: volume.estimated_chapters
          });
          
          await this.logWorkflowEvent('volume_outline_generated', {
            volume_id: volume.id,
            outline_path: volumeResult.volumeOutlinePath,
            chapters: volumeResult.chapters
          });
        } else {
          throw new Error(`分卷${volume.id}细纲生成失败: ${volumeResult.error}`);
        }
      }

      return {
        success: true,
        volumes: volumeResults,
        total_volumes: volumeResults.length
      };

    } catch (error) {
      console.error('分卷细纲生成阶段失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 执行分卷创作
   */
  async executeVolumeWriting(volumeId, writingConfig) {
    try {
      console.log(`📝 执行第${volumeId}卷创作...`);
      
      // 配置分卷创作参数
      const autoWritingConfig = {
        // 继承全局配置
        auto_outline: false, // 大纲已经生成
        auto_scene_creation: true,
        auto_draft_generation: true,
        auto_foreshadowing: true,
        auto_state_update: true,
        
        // 分卷特定配置
        target_volume: volumeId,
        chapters_per_session: writingConfig.chapters_per_batch || 5,
        words_per_chapter: writingConfig.words_per_chapter || 2000,
        pause_between_chapters: writingConfig.pause_between_chapters || false,
        
        // 用户偏好
        ...writingConfig
      };

      // 使用AutoWriter进行分卷创作
      const result = await this.autoWriter.startAutoWritingSession(autoWritingConfig);
      
      return {
        success: result.success,
        chapters_completed: this.extractChaptersCompleted(result),
        writing_log: result.steps || [],
        error: result.error
      };

    } catch (error) {
      console.error(`分卷${volumeId}创作执行失败:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取工作流程状态
   */
  async getWorkflowStatus() {
    try {
      const state = await this.loadWorkflowState();
      
      return {
        success: true,
        workflow_id: state.workflow_id,
        current_phase: state.current_phase,
        progress: state.progress,
        phases: state.phases,
        volumes: state.volumes,
        can_proceed: this.canProceedToNextPhase(state),
        next_actions: this.getAvailableActions(state)
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 暂停工作流程
   */
  async pauseWorkflow(reason = 'user_request') {
    try {
      const state = await this.loadWorkflowState();
      state.paused = true;
      state.paused_at = new Date().toISOString();
      state.pause_reason = reason;
      
      await this.saveWorkflowState(state);
      await this.logWorkflowEvent('workflow_paused', { reason });
      
      return { success: true, message: '工作流程已暂停' };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 恢复工作流程
   */
  async resumeWorkflow() {
    try {
      const state = await this.loadWorkflowState();
      state.paused = false;
      state.resumed_at = new Date().toISOString();
      delete state.pause_reason;
      
      await this.saveWorkflowState(state);
      await this.logWorkflowEvent('workflow_resumed');
      
      return { success: true, message: '工作流程已恢复' };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 应用主线大纲编辑
   */
  async applyMainOutlineEdits(edits) {
    try {
      const outlinePath = path.join(this.projectPath, '2_plot', 'main_outline.md');
      
      if (edits.content) {
        await fs.writeFile(outlinePath, edits.content, 'utf8');
      }
      
      if (edits.modifications) {
        // 处理结构化编辑
        const currentContent = await fs.readFile(outlinePath, 'utf8');
        const modifiedContent = this.applyStructuredEdits(currentContent, edits.modifications);
        await fs.writeFile(outlinePath, modifiedContent, 'utf8');
      }

    } catch (error) {
      console.error('应用主线大纲编辑失败:', error);
      throw error;
    }
  }

  /**
   * 应用分卷细纲编辑
   */
  async applyVolumeOutlineEdits(edits) {
    try {
      for (const [volumeId, volumeEdits] of Object.entries(edits)) {
        const volumeOutlinePath = path.join(this.projectPath, '2_plot', 'volumes', volumeId, 'outline.md');
        
        if (volumeEdits.content) {
          await fs.writeFile(volumeOutlinePath, volumeEdits.content, 'utf8');
        }
        
        if (volumeEdits.modifications) {
          const currentContent = await fs.readFile(volumeOutlinePath, 'utf8');
          const modifiedContent = this.applyStructuredEdits(currentContent, volumeEdits.modifications);
          await fs.writeFile(volumeOutlinePath, modifiedContent, 'utf8');
        }
      }

    } catch (error) {
      console.error('应用分卷细纲编辑失败:', error);
      throw error;
    }
  }

  /**
   * 辅助方法
   */
  
  async parseVolumesFromMainOutline(outlineContent) {
    const volumes = [];
    const volumeRegex = /\*\*第(\d+)卷[：:](.*?)\*\*/g;
    let match;

    while ((match = volumeRegex.exec(outlineContent)) !== null) {
      const volumeNumber = match[1];
      const volumeTitle = match[2].trim();
      
      volumes.push({
        id: `vol_${volumeNumber}`,
        number: parseInt(volumeNumber),
        title: volumeTitle,
        estimated_chapters: 25 // 默认值，可以从大纲中解析
      });
    }

    return volumes;
  }

  canProceedToNextPhase(state) {
    switch (state.current_phase) {
      case 'main_outline_review':
        return true;
      case 'volume_outlines_review':
        return true;
      case 'volume_writing_ready':
        return Object.values(state.volumes).some(v => !v.writing_completed);
      default:
        return false;
    }
  }

  getAvailableActions(state) {
    const actions = [];
    
    switch (state.current_phase) {
      case 'main_outline_review':
        actions.push('confirm_main_outline', 'edit_main_outline');
        break;
      case 'volume_outlines_review':
        actions.push('confirm_volume_outlines', 'edit_volume_outlines');
        break;
      case 'volume_writing_ready':
        Object.values(state.volumes).forEach(volume => {
          if (!volume.writing_completed) {
            actions.push(`start_volume_writing_${volume.id}`);
          }
        });
        break;
    }
    
    return actions;
  }

  extractChaptersCompleted(result) {
    if (result.steps) {
      const writingSteps = result.steps.filter(step => step.step === 'writing_loop');
      if (writingSteps.length > 0) {
        return writingSteps[0].chapters_created || 0;
      }
    }
    return 0;
  }

  applyStructuredEdits(content, modifications) {
    // 实现结构化编辑逻辑
    let modifiedContent = content;
    
    for (const mod of modifications) {
      switch (mod.type) {
        case 'replace_section':
          modifiedContent = this.replaceSectionInContent(modifiedContent, mod.section, mod.newContent);
          break;
        case 'insert_after':
          modifiedContent = this.insertAfterSection(modifiedContent, mod.afterSection, mod.content);
          break;
        case 'delete_section':
          modifiedContent = this.deleteSectionFromContent(modifiedContent, mod.section);
          break;
      }
    }
    
    return modifiedContent;
  }

  replaceSectionInContent(content, sectionTitle, newContent) {
    const sectionRegex = new RegExp(`(#{1,6}\\s*${sectionTitle}[\\s\\S]*?)(?=#{1,6}\\s|$)`, 'i');
    return content.replace(sectionRegex, `## ${sectionTitle}\n${newContent}\n\n`);
  }

  insertAfterSection(content, afterSection, insertContent) {
    const sectionRegex = new RegExp(`(#{1,6}\\s*${afterSection}[\\s\\S]*?)(?=#{1,6}\\s|$)`, 'i');
    const match = content.match(sectionRegex);
    if (match) {
      const insertPoint = match.index + match[0].length;
      return content.slice(0, insertPoint) + `\n\n${insertContent}\n\n` + content.slice(insertPoint);
    }
    return content;
  }

  deleteSectionFromContent(content, sectionTitle) {
    const sectionRegex = new RegExp(`#{1,6}\\s*${sectionTitle}[\\s\\S]*?(?=#{1,6}\\s|$)`, 'i');
    return content.replace(sectionRegex, '');
  }

  generateWorkflowId() {
    return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  handleError(message, error) {
    console.error(`❌ ${message}:`, error);
    return { success: false, error: `${message}: ${error}` };
  }

  async loadWorkflowState() {
    try {
      const content = await fs.readFile(this.workflowStatePath, 'utf8');
      return yaml.load(content);
    } catch (error) {
      throw new Error('无法加载工作流程状态');
    }
  }

  async saveWorkflowState(state) {
    state.last_updated = new Date().toISOString();
    await fs.writeFile(this.workflowStatePath, yaml.dump(state, { indent: 2 }), 'utf8');
  }

  async logWorkflowEvent(event, data = {}) {
    try {
      const logPath = path.join(this.projectPath, '6_automation', 'workflow_log.yml');
      
      let log = [];
      try {
        const logContent = await fs.readFile(logPath, 'utf8');
        log = yaml.load(logContent) || [];
      } catch (e) {
        // 日志文件不存在
      }

      log.push({
        timestamp: new Date().toISOString(),
        event,
        data
      });

      // 只保留最近1000条日志
      if (log.length > 1000) {
        log = log.slice(-1000);
      }

      await fs.writeFile(logPath, yaml.dump(log, { indent: 2 }), 'utf8');
    } catch (error) {
      console.error('记录工作流程日志失败:', error);
    }
  }

  async ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 导出函数接口
 */
async function initializeWorkflow(projectPath) {
  const manager = new WorkflowManager(projectPath);
  await manager.initialize();
  return manager;
}

async function startCompleteWorkflow(projectPath, config = {}) {
  const manager = new WorkflowManager(projectPath);
  await manager.initialize();
  return await manager.startCompleteWorkflow(config);
}

async function getWorkflowStatus(projectPath) {
  const manager = new WorkflowManager(projectPath);
  return await manager.getWorkflowStatus();
}

module.exports = {
  WorkflowManager,
  initializeWorkflow,
  startCompleteWorkflow,
  getWorkflowStatus
}; 
const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');
const { getApiConfig, initializeOpenAI } = require('./api-config-utils');

/**
 * 伏笔管理系统 - 追踪、分析和回收伏笔
 */
class ForeshadowingManager {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.foreshadowingPath = path.join(projectPath, '5_plot_devices', 'foreshadowing.yml');
    this.eventsPath = path.join(projectPath, '5_plot_devices', 'events.yml');
    this.plotDevicesDir = path.join(projectPath, '5_plot_devices');
  }

  /**
   * 初始化伏笔管理系统
   */
  async initialize() {
    await this.ensureDirectoryExists(this.plotDevicesDir);
    
    // 初始化伏笔文件
    if (!await this.fileExists(this.foreshadowingPath)) {
      const initialData = {
        metadata: {
          created_at: new Date().toISOString(),
          last_updated: new Date().toISOString(),
          version: '1.0.0'
        },
        active_foreshadowing: [],
        resolved_foreshadowing: [],
        planned_foreshadowing: []
      };
      await fs.writeFile(this.foreshadowingPath, yaml.dump(initialData, { indent: 2 }), 'utf8');
    }

    // 初始化事件文件
    if (!await this.fileExists(this.eventsPath)) {
      const initialEvents = {
        metadata: {
          created_at: new Date().toISOString(),
          last_updated: new Date().toISOString(),
          version: '1.0.0'
        },
        event_chains: [],
        standalone_events: []
      };
      await fs.writeFile(this.eventsPath, yaml.dump(initialEvents, { indent: 2 }), 'utf8');
    }
  }

  /**
   * 分析场景内容并提取伏笔
   */
  async analyzeSceneForForeshadowing(sceneId, content) {
    try {
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config);

      const prompt = this.buildForeshadowingAnalysisPrompt(content, sceneId);

      const completion = await openai.chat.completions.create({
        model: config.analysis_api?.model || config.generation_api?.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说伏笔分析师。请仔细分析提供的场景内容，识别其中的伏笔元素、暗示信息和潜在的故事线索。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3 // 分析用较低温度
      });

      const analysisResult = completion.choices[0].message.content;
      const extractedForeshadowing = await this.parseForeshadowingFromAI(analysisResult);

      if (extractedForeshadowing.length > 0) {
        await this.addForeshadowing(sceneId, extractedForeshadowing);
      }

      return {
        success: true,
        foreshadowing: extractedForeshadowing,
        analysisResult
      };

    } catch (error) {
      console.error('分析场景伏笔失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前可回收的伏笔（AI判断版本）
   */
  async getPayoffOpportunities(sceneData) {
    try {
      const foreshadowingData = await this.loadForeshadowingData();
      const activeForeshadowing = foreshadowingData.active_foreshadowing || [];

      if (activeForeshadowing.length === 0) {
        return { success: true, opportunities: [] };
      }

      // 使用AI分析回收机会
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config, 'analysis');
      const apiConfig = this.getApiConfig(config, 'analysis');

      // 获取上下文信息
      const context = await this.gatherPayoffAnalysisContext(sceneData);
      
      const prompt = this.buildPayoffAnalysisPrompt(activeForeshadowing, sceneData, context);

      const completion = await openai.chat.completions.create({
        model: apiConfig.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说伏笔分析师。请分析当前场景中适合回收的伏笔，考虑时机、角色状态、剧情发展等因素，给出专业的判断和建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: apiConfig.temperature || 0.3
      });

      const analysisResult = completion.choices[0].message.content;
      const opportunities = await this.parsePayoffOpportunities(analysisResult);

      return {
        success: true,
        opportunities,
        analysisResult
      };

    } catch (error) {
      console.error('获取伏笔回收机会失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成伏笔建议
   */
  async generateForeshadowingSuggestions(sceneData, context) {
    try {
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config);

      const foreshadowingData = await this.loadForeshadowingData();
      const existingForeshadowing = foreshadowingData.active_foreshadowing || [];

      const prompt = this.buildForeshadowingSuggestionPrompt(sceneData, context, existingForeshadowing);

      const completion = await openai.chat.completions.create({
        model: config.generation_api?.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说伏笔策划师。请根据当前场景和故事背景，建议适合埋设的伏笔，确保它们符合网文的节奏和读者期待。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      });

      const suggestionResult = completion.choices[0].message.content;
      const suggestions = await this.parseForeshadowingSuggestions(suggestionResult);

      return {
        success: true,
        suggestions
      };

    } catch (error) {
      console.error('生成伏笔建议失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 标记伏笔已回收
   */
  async markForeshadowingResolved(foreshadowingId, sceneId, resolutionDetails) {
    try {
      const foreshadowingData = await this.loadForeshadowingData();
      
      const activeIndex = foreshadowingData.active_foreshadowing.findIndex(f => f.id === foreshadowingId);
      if (activeIndex === -1) {
        return { success: false, error: '未找到指定的伏笔' };
      }

      const resolvedForeshadowing = foreshadowingData.active_foreshadowing[activeIndex];
      resolvedForeshadowing.resolved_in_scene = sceneId;
      resolvedForeshadowing.resolution_details = resolutionDetails;
      resolvedForeshadowing.resolved_at = new Date().toISOString();

      // 移动到已解决列表
      foreshadowingData.active_foreshadowing.splice(activeIndex, 1);
      foreshadowingData.resolved_foreshadowing.push(resolvedForeshadowing);

      foreshadowingData.metadata.last_updated = new Date().toISOString();

      await this.saveForeshadowingData(foreshadowingData);

      return {
        success: true,
        resolvedForeshadowing
      };

    } catch (error) {
      console.error('标记伏笔已回收失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 添加新伏笔
   */
  async addForeshadowing(sceneId, foreshadowingList) {
    try {
      const foreshadowingData = await this.loadForeshadowingData();

      for (const foreshadowing of foreshadowingList) {
        const newForeshadowing = {
          id: this.generateForeshadowingId(),
          planted_in_scene: sceneId,
          planted_at: new Date().toISOString(),
          type: foreshadowing.type,
          description: foreshadowing.description,
          hints: foreshadowing.hints || [],
          importance: foreshadowing.importance || 5,
          estimated_payoff_distance: foreshadowing.estimated_payoff_distance || 3,
          tags: foreshadowing.tags || [],
          relevant_characters: foreshadowing.relevant_characters || [],
          relevant_locations: foreshadowing.relevant_locations || [],
          status: 'active'
        };

        foreshadowingData.active_foreshadowing.push(newForeshadowing);
      }

      foreshadowingData.metadata.last_updated = new Date().toISOString();
      await this.saveForeshadowingData(foreshadowingData);

      return {
        success: true,
        addedCount: foreshadowingList.length
      };

    } catch (error) {
      console.error('添加伏笔失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 构建伏笔分析的Prompt
   */
  buildForeshadowingAnalysisPrompt(content, sceneId) {
    return `请分析以下场景内容，识别其中的伏笔元素和暗示信息：

## 场景ID
${sceneId}

## 场景内容
${content}

## 分析要求

请仔细分析上述内容，识别以下类型的伏笔和暗示：

1. **角色秘密** - 角色隐藏的身份、过往、动机
2. **物品线索** - 重要物品的暗示、特殊能力的提及
3. **事件预示** - 对未来事件的暗示或预兆
4. **能力暗示** - 角色潜在能力的暗示
5. **关系线索** - 角色关系的深层暗示
6. **世界观扩展** - 对世界观新元素的暗示
7. **情节转折预示** - 对故事转折的铺垫

请以YAML格式返回分析结果：

\`\`\`yaml
foreshadowing_analysis:
  detected_foreshadowing:
    - type: "character_secret"  # 伏笔类型
      description: "简要描述这个伏笔的内容"
      hints: 
        - "文本中的具体暗示1"
        - "文本中的具体暗示2"
      importance: 8  # 1-10的重要程度评分
      estimated_payoff_distance: 3  # 预期在几章后回收
      tags: ["标签1", "标签2"]
      relevant_characters: ["相关角色1", "相关角色2"]
      relevant_locations: ["相关地点1"]
      confidence: 0.8  # 识别置信度 0-1
    
  potential_foreshadowing:
    - type: "event_foreshadowing"
      description: "可能的伏笔内容"
      hints: ["可能的暗示"]
      confidence: 0.5
      
  analysis_notes: "其他分析说明"
\`\`\`

注意：
- 只识别真正的伏笔，不要将普通的剧情发展误认为伏笔
- 重点关注暗示性、象征性的内容
- 考虑网络小说的创作特点，注重爽点和冲突的铺垫
- 置信度低于0.6的归类为潜在伏笔`;
  }

  /**
   * 构建伏笔建议的Prompt
   */
  buildForeshadowingSuggestionPrompt(sceneData, context, existingForeshadowing) {
    let prompt = `请为当前场景建议适合埋设的伏笔：

## 当前场景信息
- 场景ID: ${sceneData.scene_id}
- 参与角色: ${sceneData.characters?.join(', ') || '未指定'}
- 场景地点: ${sceneData.location || '未指定'}
- 场景目标: ${sceneData.goal || '未指定'}

## 故事背景
${context.worldBible || '暂无世界观设定'}

## 现有伏笔
`;

    if (existingForeshadowing.length > 0) {
      existingForeshadowing.forEach(f => {
        prompt += `- ${f.type}: ${f.description} (重要性: ${f.importance}/10)\n`;
      });
    } else {
      prompt += '暂无活跃伏笔\n';
    }

    prompt += `
## 建议要求

请根据当前场景特点和故事发展需要，建议适合在本场景埋设的伏笔，包括：

1. **短期伏笔** (1-3章后回收) - 为近期剧情服务
2. **中期伏笔** (3-8章后回收) - 为本卷重要情节服务  
3. **长期伏笔** (8章以上回收) - 为整个故事服务

请以YAML格式返回建议：

\`\`\`yaml
foreshadowing_suggestions:
  short_term:
    - type: "character_secret"
      description: "建议的伏笔描述"
      implementation_hints: 
        - "具体的实施建议1"
        - "具体的实施建议2"
      importance: 7
      estimated_payoff_distance: 2
      rationale: "为什么在这里埋设这个伏笔"
      
  medium_term:
    - type: "ability_hint"
      description: "中期伏笔描述"
      implementation_hints: ["实施建议"]
      importance: 8
      estimated_payoff_distance: 5
      rationale: "埋设理由"
      
  long_term:
    - type: "world_expansion"
      description: "长期伏笔描述"
      implementation_hints: ["实施建议"]
      importance: 9
      estimated_payoff_distance: 15
      rationale: "埋设理由"
      
  embedding_strategy: "建议的整体埋设策略，如何自然地融入当前场景"
\`\`\`

注意：
- 伏笔要符合当前场景的自然流程
- 不要过于明显，保持暗示性
- 考虑角色的性格和当前状态
- 确保伏笔有实际的故事价值`;

    return prompt;
  }

  /**
   * 收集回收分析所需的上下文信息
   */
  async gatherPayoffAnalysisContext(sceneData) {
    const context = {
      recentChapters: [],
      characterStates: [],
      worldBible: '',
      volumeOutline: ''
    };

    try {
      // 获取最近几章内容
      const publishedDir = path.join(this.projectPath, '3_manuscript', 'published');
      const publishedFiles = await fs.readdir(publishedDir);
      const recentFiles = publishedFiles
        .filter(f => f.endsWith('.md'))
        .sort()
        .slice(-3); // 最近3章

      for (const file of recentFiles) {
        const filePath = path.join(publishedDir, file);
        const content = await fs.readFile(filePath, 'utf8');
        context.recentChapters.push({
          filename: file,
          content: content.substring(0, 1000) // 只取前1000字符
        });
      }

      // 获取角色状态
      const charactersDir = path.join(this.projectPath, '1_knowledge_base', 'characters');
      const characterFiles = await fs.readdir(charactersDir);
      for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
        const charPath = path.join(charactersDir, file);
        const charContent = await fs.readFile(charPath, 'utf8');
        const charData = yaml.load(charContent);
        context.characterStates.push(charData);
      }

      // 获取世界观设定（摘要）
      const worldBiblePath = path.join(this.projectPath, '1_knowledge_base', 'world_bible.md');
      try {
        const worldBible = await fs.readFile(worldBiblePath, 'utf8');
        context.worldBible = worldBible.substring(0, 1500); // 取前1500字符
      } catch (e) {
        // 文件不存在时忽略
      }

    } catch (error) {
      console.warn('收集上下文信息失败:', error);
    }

    return context;
  }

  /**
   * 构建回收分析的Prompt
   */
  buildPayoffAnalysisPrompt(activeForeshadowing, sceneData, context) {
    let prompt = `请分析当前场景中适合回收的伏笔：

## 当前场景信息
- 场景ID: ${sceneData.scene_id}
- 参与角色: ${sceneData.characters?.join(', ') || '未指定'}
- 场景地点: ${sceneData.location || '未指定'}
- 场景目标: ${sceneData.goal || '未指定'}
- 场景描述: ${sceneData.description || '未指定'}

## 活跃伏笔列表
`;

    activeForeshadowing.forEach((f, index) => {
      const plantedChapters = this.getChapterDistance(f.planted_in_scene, sceneData.scene_id);
      prompt += `### 伏笔 ${index + 1}
- ID: ${f.id}
- 类型: ${f.type}
- 描述: ${f.description}
- 重要性: ${f.importance}/10
- 埋设场景: ${f.planted_in_scene}
- 距离埋设: ${plantedChapters}章
- 预期回收距离: ${f.estimated_payoff_distance}章
- 相关角色: ${f.relevant_characters?.join(', ') || '无'}
- 相关地点: ${f.relevant_locations?.join(', ') || '无'}
- 具体暗示: ${f.hints?.join('；') || '无'}
- 标签: ${f.tags?.join(', ') || '无'}

`;
    });

    prompt += `## 当前故事上下文
`;

    if (context.recentChapters.length > 0) {
      prompt += `### 最近章节摘要\n`;
      context.recentChapters.forEach((chapter, index) => {
        prompt += `**${chapter.filename}**\n${chapter.content}\n\n`;
      });
    }

    if (context.characterStates.length > 0) {
      prompt += `### 当前角色状态\n`;
      context.characterStates.forEach(char => {
        if (sceneData.characters && sceneData.characters.includes(char.name)) {
          prompt += `**${char.name}**\n`;
          prompt += `- 当前位置: ${char.state?.location || '未知'}\n`;
          prompt += `- 情绪状态: ${char.state?.emotion || '未知'}\n`;
          prompt += `- 当前状态: ${char.state?.current_status || '未设定'}\n\n`;
        }
      });
    }

    if (context.worldBible) {
      prompt += `### 世界观背景摘要\n${context.worldBible}\n\n`;
    }

    prompt += `## 分析要求

请根据以上信息，分析每个伏笔在当前场景的回收适宜性，并以YAML格式返回：

\`\`\`yaml
payoff_analysis:
  suitable_payoffs:
    - foreshadowing_id: "伏笔ID"
      suitability_score: 0.85  # 适宜性评分 0-1
      timing_assessment: "excellent"  # 时机评估: poor/fair/good/excellent
      payoff_type: "partial"  # 回收类型: full/partial/hint_only
      reasoning: "为什么适合在此时回收的详细分析"
      payoff_suggestion: "具体的回收建议，如何在场景中自然地揭示"
      character_reactions: ["角色1的反应", "角色2的反应"]
      plot_impact: "对后续剧情的影响"
      priority: "high"  # 优先级: low/medium/high/urgent
      
  unsuitable_payoffs:
    - foreshadowing_id: "伏笔ID"
      reasoning: "为什么不适合在此时回收"
      better_timing: "更好的回收时机建议"
      
  recommendations:
    max_payoffs_this_scene: 2  # 建议本场景最多回收几个伏笔
    pacing_advice: "节奏控制建议"
    future_setup: "为未来回收做的铺垫建议"
\`\`\`

评判标准：
1. 时机合理性：角色状态、剧情发展、情感氛围是否适合
2. 逻辑连贯性：回收是否符合故事逻辑和角色认知
3. 影响评估：回收对当前场景和后续剧情的影响
4. 节奏控制：避免在单个场景回收过多伏笔
5. 读者体验：回收是否能增强阅读快感和满足感`;

    return prompt;
  }

  /**
   * 解析AI返回的回收机会分析
   */
  async parsePayoffOpportunities(analysisResult) {
    try {
      let yamlContent = analysisResult;
      
      const yamlMatch = analysisResult.match(/```yaml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```yml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```\n([\s\S]*?)\n```/);
      
      if (yamlMatch) {
        yamlContent = yamlMatch[1];
      }

      const parsed = yaml.load(yamlContent);
      const suitablePayoffs = parsed?.payoff_analysis?.suitable_payoffs || [];
      
      // 转换为统一格式
      const opportunities = suitablePayoffs.map(payoff => ({
        foreshadowing_id: payoff.foreshadowing_id,
        suitability_score: payoff.suitability_score || 0,
        timing_assessment: payoff.timing_assessment || 'fair',
        payoff_type: payoff.payoff_type || 'partial',
        reasoning: payoff.reasoning || '',
        payoff_suggestion: payoff.payoff_suggestion || '',
        character_reactions: payoff.character_reactions || [],
        plot_impact: payoff.plot_impact || '',
        priority: payoff.priority || 'medium'
      }));

      // 按适宜性评分排序
      opportunities.sort((a, b) => b.suitability_score - a.suitability_score);

      return opportunities;

    } catch (error) {
      console.error('解析回收机会分析失败:', error);
      return [];
    }
  }

  /**
   * 生成回收建议
   */
  async generatePayoffSuggestion(foreshadowing, sceneData) {
    try {
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config);

      const prompt = `请为以下伏笔生成回收建议：

## 伏笔信息
- 类型: ${foreshadowing.type}
- 描述: ${foreshadowing.description}
- 埋设位置: ${foreshadowing.planted_in_scene}
- 暗示内容: ${foreshadowing.hints.join(', ')}
- 重要性: ${foreshadowing.importance}/10

## 当前场景
- 场景ID: ${sceneData.scene_id}
- 参与角色: ${sceneData.characters?.join(', ') || '未指定'}
- 场景地点: ${sceneData.location || '未指定'}
- 场景目标: ${sceneData.goal || '未指定'}

请生成一个自然的伏笔回收方案，说明如何在当前场景中揭示或推进这个伏笔。要求：
1. 回收方式要符合当前场景的逻辑
2. 不要过于突兀，要自然融入剧情
3. 考虑角色的反应和情感变化
4. 如果是部分回收，说明还保留哪些悬念

回收建议：`;

      const completion = await openai.chat.completions.create({
        model: config.generation_api?.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说伏笔回收专家。请提供自然、合理的伏笔回收建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7
      });

      return completion.choices[0].message.content;

    } catch (error) {
      console.error('生成回收建议失败:', error);
      return '无法生成回收建议';
    }
  }

  /**
   * 解析AI返回的伏笔分析结果
   */
  async parseForeshadowingFromAI(analysisResult) {
    try {
      let yamlContent = analysisResult;
      
      // 提取YAML代码块
      const yamlMatch = analysisResult.match(/```yaml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```yml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```\n([\s\S]*?)\n```/);
      
      if (yamlMatch) {
        yamlContent = yamlMatch[1];
      }

      const parsed = yaml.load(yamlContent);
      const detectedForeshadowing = parsed?.foreshadowing_analysis?.detected_foreshadowing || [];
      
      // 过滤置信度过低的结果
      return detectedForeshadowing.filter(f => (f.confidence || 1) >= 0.6);

    } catch (error) {
      console.error('解析伏笔分析结果失败:', error);
      return [];
    }
  }

  /**
   * 解析伏笔建议
   */
  async parseForeshadowingSuggestions(suggestionResult) {
    try {
      let yamlContent = suggestionResult;
      
      const yamlMatch = suggestionResult.match(/```yaml\n([\s\S]*?)\n```/) ||
                       suggestionResult.match(/```yml\n([\s\S]*?)\n```/) ||
                       suggestionResult.match(/```\n([\s\S]*?)\n```/);
      
      if (yamlMatch) {
        yamlContent = yamlMatch[1];
      }

      const parsed = yaml.load(yamlContent);
      return parsed?.foreshadowing_suggestions || {};

    } catch (error) {
      console.error('解析伏笔建议失败:', error);
      return {};
    }
  }

  /**
   * 计算角色匹配度
   */
  calculateCharacterMatch(relevantCharacters, sceneCharacters) {
    if (!relevantCharacters || !sceneCharacters || relevantCharacters.length === 0) {
      return 0;
    }

    const matchCount = relevantCharacters.filter(char => sceneCharacters.includes(char)).length;
    return matchCount / relevantCharacters.length;
  }

  /**
   * 计算章节距离
   */
  getChapterDistance(fromSceneId, toSceneId) {
    const fromChapter = this.extractChapterNumber(fromSceneId);
    const toChapter = this.extractChapterNumber(toSceneId);
    
    if (fromChapter === null || toChapter === null) {
      return 0;
    }
    
    return Math.abs(toChapter - fromChapter);
  }

  /**
   * 提取章节号
   */
  extractChapterNumber(sceneId) {
    const match = sceneId.match(/第(\d+)章/);
    return match ? parseInt(match[1]) : null;
  }

  /**
   * 生成伏笔ID
   */
  generateForeshadowingId() {
    return `foreshadowing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 加载伏笔数据
   */
  async loadForeshadowingData() {
    try {
      const content = await fs.readFile(this.foreshadowingPath, 'utf8');
      return yaml.load(content);
    } catch (error) {
      console.error('加载伏笔数据失败:', error);
      return {
        metadata: {},
        active_foreshadowing: [],
        resolved_foreshadowing: [],
        planned_foreshadowing: []
      };
    }
  }

  /**
   * 保存伏笔数据
   */
  async saveForeshadowingData(data) {
    await fs.writeFile(this.foreshadowingPath, yaml.dump(data, { indent: 2 }), 'utf8');
  }

  /**
   * 辅助方法
   */
  async loadConfig() {
    const configPath = path.join(this.projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    return yaml.load(configContent);
  }

  initializeOpenAI(config, apiType = 'analysis') {
    return initializeOpenAI(config, apiType);
  }

  /**
   * 获取API配置
   */
  getApiConfig(config, apiType = 'analysis') {
    return getApiConfig(config, apiType);
  }

  async ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 导出函数接口
 */
async function initializeForeshadowingManager(projectPath) {
  const manager = new ForeshadowingManager(projectPath);
  await manager.initialize();
  return manager;
}

async function analyzeSceneForForeshadowing(projectPath, sceneId, content) {
  const manager = new ForeshadowingManager(projectPath);
  return await manager.analyzeSceneForForeshadowing(sceneId, content);
}

async function getPayoffOpportunities(projectPath, sceneData) {
  const manager = new ForeshadowingManager(projectPath);
  return await manager.getPayoffOpportunities(sceneData);
}

async function generateForeshadowingSuggestions(projectPath, sceneData, context) {
  const manager = new ForeshadowingManager(projectPath);
  return await manager.generateForeshadowingSuggestions(sceneData, context);
}

module.exports = {
  ForeshadowingManager,
  initializeForeshadowingManager,
  analyzeSceneForForeshadowing,
  getPayoffOpportunities,
  generateForeshadowingSuggestions
}; 
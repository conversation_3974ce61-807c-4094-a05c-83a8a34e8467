const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');
const { getApiConfig, initializeOpenAI } = require('./api-config-utils');

// AI创建场景的核心函数
async function aiCreateScene(projectPath, userParams = {}) {
  try {
    // 1. 读取项目配置
    const configPath = path.join(projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    // 向后兼容：处理旧的api_settings配置
    if (config.api_settings && !config.generation_api) {
      config.generation_api = {
        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
        api_key: config.api_settings.api_key || '',
        model: config.api_settings.model || 'gpt-3.5-turbo',
        temperature: config.api_settings.temperature || 0.7
      };
    }

    // 2. 初始化OpenAI客户端（使用统一的API配置工具）
    const apiType = 'primary_generation';
    const apiConfig = getApiConfig(config, apiType);
    const openai = initializeOpenAI(config, apiType);

    // 3. 汇编上下文信息（支持排除角色）
    const context = await compileProjectContext(projectPath, userParams.excludeCharacters);

    // 4. 构建AI创建场景的prompt
    const prompt = buildAiCreateScenePrompt(context, userParams);

    // 调试：检查请求参数
    const requestParams = {
      model: config.generation_api.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的小说创作助手。请根据提供的项目信息，创建一个新的场景卡片，包括场景ID、角色、地点、目标等信息。返回结果必须是有效的JSON格式。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: config.generation_api.temperature || 0.7
    };
    
    console.log('请求参数:', {
      model: requestParams.model,
      temperature: requestParams.temperature,
      systemMessageLength: requestParams.messages[0].content.length,
      userMessageLength: requestParams.messages[1].content.length,
      totalMessages: requestParams.messages.length
    });

    // 5. 调用AI生成
    let completion;
    try {
      completion = await openai.chat.completions.create(requestParams);
    } catch (apiError) {
      console.error('OpenAI API调用失败:', {
        status: apiError.status,
        message: apiError.message,
        type: apiError.type,
        code: apiError.code,
        param: apiError.param,
        headers: apiError.headers
      });
      
      // 根据错误类型提供更具体的错误信息
      if (apiError.status === 400) {
        return { success: false, error: `API请求格式错误: ${apiError.message || '请检查模型名称和参数设置'}` };
      } else if (apiError.status === 401) {
        return { success: false, error: 'API密钥无效，请检查密钥是否正确' };
      } else if (apiError.status === 403) {
        return { success: false, error: 'API访问被拒绝，请检查密钥权限' };
      } else if (apiError.status === 429) {
        return { success: false, error: 'API调用频率超限，请稍后重试' };
      } else if (apiError.status === 500) {
        return { success: false, error: 'OpenAI服务器内部错误，请稍后重试' };
      } else {
        return { success: false, error: `API调用失败: ${apiError.message}` };
      }
    }

    console.log('AI响应:', completion.choices[0].message.content);
    const aiResponse = completion.choices[0].message.content;

    // 6. 解析AI返回的JSON
    let sceneData;
    try {
      // 提取JSON部分
      const jsonMatch = aiResponse.match(/```json\n([\s\S]*?)\n```/) ||
                       aiResponse.match(/```\n([\s\S]*?)\n```/) ||
                       aiResponse.match(/```json([\s\S]*?)```/);

      if (jsonMatch) {
        sceneData = JSON.parse(jsonMatch[1]);
      } else {
        console.error('AI响应中未找到有效的JSON，原始响应：', aiResponse)
        return { success: false, error: '无法解析AI创建结果' };
      }
    } catch (parseError) {
      console.error('解析AI创建结果失败:', parseError);
      return { success: false, error: '无法解析AI创建结果' };
    }

    // 7. 添加AI生成标签
    sceneData.ai_generated = true;

    return {
      success: true,
      sceneData
    };

  } catch (error) {
    console.error('AI创建场景失败:', error);
    return { success: false, error: error.message };
  }
}

// 汇编项目上下文信息
async function compileProjectContext(projectPath, excludeCharacters = []) {
  const context = {
    worldBible: '',
    outline: '',
    characters: [],
    locations: [],
    organizations: [],
    publishedChapters: []
  };

  try {
    // 读取世界观设定
    const worldBiblePath = path.join(projectPath, '1_knowledge_base', 'world_bible.md');
    try {
      context.worldBible = await fs.readFile(worldBiblePath, 'utf8');
    } catch (e) {
      // 文件不存在时忽略
    }

    // 读取主线大纲
    const outlinePath = path.join(projectPath, '2_plot', 'main_outline.md');
    try {
      context.outline = await fs.readFile(outlinePath, 'utf8');
    } catch (e) {
      // 文件不存在时忽略
    }

    // 读取角色信息（支持排除指定角色）
    const charactersDir = path.join(projectPath, '1_knowledge_base', 'characters');
    try {
      const characterFiles = await fs.readdir(charactersDir);
      for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
        const charPath = path.join(charactersDir, file);
        const charContent = await fs.readFile(charPath, 'utf8');
        const charData = yaml.load(charContent);
        
        // 检查是否需要排除此角色
        if (!excludeCharacters.includes(charData.name)) {
          context.characters.push(charData);
        }
      }
    } catch (e) {
      // 目录不存在时忽略
    }

    // 读取地点信息
    const locationsDir = path.join(projectPath, '1_knowledge_base', 'locations');
    try {
      const locationFiles = await fs.readdir(locationsDir);
      for (const file of locationFiles.filter(f => f.endsWith('.yml'))) {
        const locPath = path.join(locationsDir, file);
        const locContent = await fs.readFile(locPath, 'utf8');
        const locData = yaml.load(locContent);
        context.locations.push(locData);
      }
    } catch (e) {
      // 目录不存在时忽略
    }

    // 读取组织信息
    const organizationsDir = path.join(projectPath, '1_knowledge_base', 'organizations');
    try {
      const orgFiles = await fs.readdir(organizationsDir);
      for (const file of orgFiles.filter(f => f.endsWith('.yml'))) {
        const orgPath = path.join(organizationsDir, file);
        const orgContent = await fs.readFile(orgPath, 'utf8');
        const orgData = yaml.load(orgContent);
        context.organizations.push(orgData);
      }
    } catch (e) {
      // 目录不存在时忽略
    }

    // 读取已发布的章节（最近3个）
    const publishedDir = path.join(projectPath, '3_manuscript', 'published');
    try {
      const publishedFiles = await fs.readdir(publishedDir);
      const mdFiles = publishedFiles
        .filter(f => f.endsWith('.md'))
        .sort((a, b) => {
          const numA = getChapterNumber(a);
          const numB = getChapterNumber(b);
          return numA - numB;
        })
        .slice(-3);

      for (const file of mdFiles) {
        const filePath = path.join(publishedDir, file);
        const content = await fs.readFile(filePath, 'utf8');
        context.publishedChapters.push({
          filename: file,
          content: content
        });
      }
    } catch (e) {
      // 目录不存在时忽略
    }

  } catch (error) {
    console.error('汇编项目上下文失败:', error);
  }

  return context;
}

// 从文件名中提取章节号（支持中文和阿拉伯数字）
function getChapterNumber(filename) {
  const match = filename.match(/^第(.*?)章/);
  if (!match) return 0;

  const chapterStr = match[1];
  const num = parseInt(chapterStr, 10);
  if (!isNaN(num)) {
    return num;
  }

  const chnMap = { '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9 };
  
  if (chapterStr.startsWith('十')) {
    if (chapterStr.length === 1) return 10;
    return 10 + (chnMap[chapterStr[1]] || 0);
  }
  if (chapterStr.endsWith('十')) {
    return (chnMap[chapterStr[0]] || 0) * 10;
  }
  if (chapterStr.length === 3 && chapterStr[1] === '十') {
    return (chnMap[chapterStr[0]] || 0) * 10 + (chnMap[chapterStr[2]] || 0);
  }
  if(chnMap[chapterStr] !== undefined) {
    return chnMap[chapterStr];
  }

  return 0; // 无法解析时返回0
}

// 构建AI创建场景的prompt
function buildAiCreateScenePrompt(context, userParams = {}) {

  let prompt = `请基于以下项目信息，创建一个新的场景卡片：
## 世界观设定
${context.worldBible || '暂无世界观设定'}

## 主线大纲
${context.outline || '暂无主线大纲'}


## 现有角色
`;

  if (context.characters.length > 0) {
    context.characters.forEach(char => {
      for (const key of Object.keys(char)) {
        if (key !== 'name') {
          prompt += `- ${char.name}的${key}: ${char[key]}\n`;
        }
      }
    });
  } else {
    prompt += '暂无角色信息\n';
  }

  prompt += '\n## 现有地点\n';
  if (context.locations.length > 0) {
    context.locations.forEach(loc => {
      for (const key of Object.keys(loc)) {
        if (key !== 'name') {
          prompt += `- ${loc.name}的${key}: ${loc[key]}\n`;
        }
      }
    });
  } else {
    prompt += '暂无地点信息\n';
  }

  prompt += '\n## 现有组织\n';
  if (context.organizations.length > 0) {
    context.organizations.forEach(org => {
      for (const key of Object.keys(org)) {
        if (key !== 'name') {
          prompt += `- ${org.name}的${key}: ${org[key]}\n`;
        }
      }
    });
  } else {
    prompt += '暂无组织信息\n';
  }

  if (context.publishedChapters.length > 0) {
    prompt += '\n## 已发布章节参考（最近三章）\n';
    context.publishedChapters.forEach((chapter, index) => {
      prompt += chapter.content + '\n\n';
    });
  }

  prompt += `
## 创建要求
请创建一个新的场景卡片，包含以下信息，并以JSON格式返回：

\`\`\`json
{
  "scene_id": "场景ID（如第1章_xxx）",
  "storyline": "故事线（通常是main）",
  "characters": ["参与的角色名称列表"],
  "location": "场景发生的地点",
  "goals": "场景要达成的目标描述",
  "description": "场景的详细描述",
  "suggested_new_characters": [
    {
      "name": "角色名称",
      "description": "角色描述（包括组织归属）",
      "motivation": "角色动机",
      "appearance": "外貌描述",
      "knowledge": [],
      "beliefs": [],
      "secrets": [],
      "state": {
        "emotion": "初始情绪",
        "location": "初始位置",
        "current_status": "当前状态描述",
        "inventory": []
      },
      "relationships": {}
    }
  ],
  "suggested_new_locations": [
    {
      "name": "地点名称",
      "type": "地点类型",
      "description": "地点描述",
      "atmosphere": "氛围"
    }
  ],
  "suggested_new_organizations": [
    {
      "name": "组织名称",
      "type": "组织类型",
      "description": "组织描述",
      "leader": "领导者",
      "influence_level": "影响力等级",
      "status": "组织状态"
    }
  ]
}
\`\`\`

## 用户指定的参数
${userParams.scene_id ? `- 场景ID: ${userParams.scene_id}` : '- 场景ID: 由AI自动生成'}
${userParams.characters && userParams.characters.length > 0 ? `- 参与角色: ${userParams.characters.join(', ')}` : '- 参与角色: 由AI根据剧情需要选择'}
${userParams.location ? `- 场景地点: ${userParams.location}` : '- 场景地点: 由AI自动选择'}
${userParams.goals ? `- 场景目标: ${userParams.goals}` : '- 场景目标: 由AI自动设定'}
${userParams.description ? `- 场景简述: ${userParams.description}` : '- 场景简述: 由AI自动生成'}

注意：
1. 场景应该符合已有的世界观和故事发展
2. 如果需要新角色、地点或组织，请在对应的suggested数组中提供
3. 确保场景有明确的目标和冲突
4. 场景描述要生动具体，有助于后续的文本生成
5. 请优先使用用户指定的参数，如果用户未指定则由AI自动生成
6. 用户指定的角色必须在参与角色列表中，地点必须在现有地点中选择或创建新地点`;

  return prompt;
}

module.exports = {
  aiCreateScene,
  compileProjectContext,
  buildAiCreateScenePrompt
};

const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');
const { getApiConfig, initializeOpenAI } = require('./api-config-utils');

// 引入新模块
const { getCurrentVolumeOutline } = require('./ai-outline-generator');
const { getPayoffOpportunities, generateForeshadowingSuggestions } = require('./foreshadowing-manager');

// AI生成场景初稿的核心函数（增强版，支持合并分析功能）
async function generateSceneDraft(projectPath, sceneData, options = {}) {
  try {
    // 1. 读取项目配置
    const configPath = path.join(projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    // 向后兼容：处理旧的api_settings配置
    if (config.api_settings && !config.generation_api) {
      config.generation_api = {
        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
        api_key: config.api_settings.api_key || '',
        model: config.api_settings.model || 'gpt-3.5-turbo',
        temperature: config.api_settings.temperature || 0.7
      };
    }

    // 确定使用的API配置
    const apiType = 'primary_generation';
    const apiConfig = getApiConfig(config, apiType);

    // 2. 初始化OpenAI客户端
    const openai = initializeOpenAI(config, apiType);

    // 3. 汇编上下文信息
    const context = await compileSceneContext(projectPath, sceneData);

    // 4. 检查是否启用合并分析功能
    const enableCombinedAnalysis = config.modules?.draft_generator?.combined_analysis || options.combinedAnalysis;

    // 5. 构建prompt（根据是否合并功能决定prompt内容）
    const prompt = enableCombinedAnalysis 
      ? buildEnhancedScenePrompt(context, sceneData, config)
      : buildScenePrompt(context, sceneData);

    // 6. 构建系统消息
    const systemMessage = enableCombinedAnalysis
      ? buildEnhancedSystemMessage()
      : '你是一个专业的小说写作助手。请根据提供的场景信息和上下文，生成一个生动、符合角色性格和故事背景的场景文本。';

    // 7. 调用AI生成
    const completion = await openai.chat.completions.create({
      model: apiConfig.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: systemMessage
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: apiConfig.temperature || 0.7,
      max_tokens: apiConfig.max_tokens || 4000
    });

    const aiResponse = completion.choices[0].message.content;

    // 8. 解析AI响应
    let result;
    if (enableCombinedAnalysis) {
      result = await parseEnhancedResponse(aiResponse, projectPath, sceneData);
    } else {
      // 传统单一生成模式
      const draftPath = path.join(projectPath, '3_manuscript', 'drafts', `${sceneData.scene_id}.md`);
      await fs.writeFile(draftPath, aiResponse, 'utf8');
      
      result = {
        success: true,
        draftPath,
        content: aiResponse
      };
    }

    return result;

  } catch (error) {
    console.error('AI生成失败:', error);
    return { success: false, error: error.message };
  }
}

// 汇编场景上下文信息
async function compileSceneContext(projectPath, sceneData) {
  const context = {
    worldBible: '',
    outline: '',
    volumeOutline: '',
    characters: {},
    locations: {},
    organizations: {},
    previousScenes: [],
    foreshadowing: {
      payoffOpportunities: [],
      suggestions: {}
    }
  };

  try {
    // 读取世界观设定
    const worldBiblePath = path.join(projectPath, '1_knowledge_base', 'world_bible.md');
    try {
      context.worldBible = await fs.readFile(worldBiblePath, 'utf8');
    } catch (e) {
      // 文件不存在时忽略
    }

    // 读取主线大纲
    const outlinePath = path.join(projectPath, '2_plot', 'main_outline.md');
    try {
      context.outline = await fs.readFile(outlinePath, 'utf8');
    } catch (e) {
      // 文件不存在时忽略
    }

    // 读取相关角色信息
    if (sceneData.characters && sceneData.characters.length > 0) {
      // 先读取所有角色文件，然后根据名称匹配
      const charactersDir = path.join(projectPath, '1_knowledge_base', 'characters');
      try {
        const characterFiles = await fs.readdir(charactersDir);
        for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
          const charPath = path.join(charactersDir, file);
          try {
            const charContent = await fs.readFile(charPath, 'utf8');
            const charData = yaml.load(charContent);
            // 如果角色名称在场景的角色列表中，则添加到上下文
            if (charData.name && sceneData.characters.includes(charData.name)) {
              context.characters[charData.name] = charData;
            }
          } catch (e) {
            console.warn(`无法读取角色文件: ${file}`);
          }
        }
      } catch (e) {
        console.warn('无法读取角色目录');
      }
    }

    // 读取相关地点信息
    if (sceneData.location) {
      const locationsDir = path.join(projectPath, '1_knowledge_base', 'locations');
      try {
        const locationFiles = await fs.readdir(locationsDir);
        for (const file of locationFiles.filter(f => f.endsWith('.yml'))) {
          const locPath = path.join(locationsDir, file);
          try {
            const locContent = await fs.readFile(locPath, 'utf8');
            const locData = yaml.load(locContent);
            if (locData.name === sceneData.location) {
              context.locations[locData.name] = locData;
              break;
            }
          } catch (e) {
            console.warn(`无法读取地点文件: ${file}`);
          }
        }
      } catch (e) {
        console.warn('无法读取地点目录');
      }
    }

    // 读取相关组织信息
    const organizationsDir = path.join(projectPath, '1_knowledge_base', 'organizations');
    try {
      const orgFiles = await fs.readdir(organizationsDir);
      for (const file of orgFiles.filter(f => f.endsWith('.yml'))) {
        const orgPath = path.join(organizationsDir, file);
        try {
          const orgContent = await fs.readFile(orgPath, 'utf8');
          const orgData = yaml.load(orgContent);
          if (orgData.name) {
            context.organizations[orgData.name] = orgData;
          }
        } catch (e) {
          console.warn(`无法读取组织文件: ${file}`);
        }
      }
    } catch (e) {
      console.warn('无法读取组织目录');
    }

    // 读取当前分卷细纲
    try {
      const volumeOutline = await getCurrentVolumeOutline(projectPath, sceneData.scene_id);
      if (volumeOutline) {
        context.volumeOutline = volumeOutline.content;
      }
    } catch (e) {
      console.warn('获取分卷细纲失败:', e.message);
    }

    // 获取伏笔信息
    try {
      const payoffResult = await getPayoffOpportunities(projectPath, sceneData);
      if (payoffResult.success) {
        context.foreshadowing.payoffOpportunities = payoffResult.opportunities || [];
      }

      const suggestionResult = await generateForeshadowingSuggestions(projectPath, sceneData, context);
      if (suggestionResult.success) {
        context.foreshadowing.suggestions = suggestionResult.suggestions || {};
      }
    } catch (e) {
      console.warn('获取伏笔信息失败:', e.message);
    }

    // 读取已发布的章节作为上下文（最近3个）
    const publishedDir = path.join(projectPath, '3_manuscript', 'published');
    try {
      const publishedFiles = await fs.readdir(publishedDir);
      const mdFiles = publishedFiles.filter(f => f.endsWith('.md')).slice(-3);

      for (const file of mdFiles) {
        const filePath = path.join(publishedDir, file);
        const content = await fs.readFile(filePath, 'utf8');
        context.previousScenes.push({
          filename: file,
          content: content
        });
      }
    } catch (e) {
      // 目录不存在时忽略
    }

  } catch (error) {
    console.error('汇编上下文失败:', error);
  }

  return context;
}

// 构建场景生成的prompt
function buildScenePrompt(context, sceneData) {
  let prompt = `请为以下场景生成一个生动的小说片段：

## 场景信息
- 场景ID: ${sceneData.scene_id}
- 故事线: ${sceneData.storyline}
- 地点: ${sceneData.location || '未指定'}

## 场景目标
`;

  if (sceneData.goal && sceneData.goal.trim()) {
    prompt += `- ${sceneData.goal}\n`;
  } else {
    prompt += '- 未指定具体目标\n';
  }

  // 添加角色信息
  if (Object.keys(context.characters).length > 0) {
    prompt += '\n## 参与角色\n';
    Object.entries(context.characters).forEach(([id, character]) => {
      for (const key of Object.keys(character)) {
        if (key !== 'name') {
          prompt += `- ${character.name}的${key}: ${character[key]}\n`;
        }
      }
      prompt += '\n';
    });
  }

  // 添加地点信息
  if (Object.keys(context.locations).length > 0) {
    prompt += '\n## 场景地点\n';
    Object.entries(context.locations).forEach(([id, location]) => {
      for (const key of Object.keys(location)) {
        if (key !== 'name') {
          prompt += `- ${location.name}的${key}: ${location[key]}\n`;
        }
      }
      prompt += '\n';
    });
  }

  // 添加组织信息
  if (Object.keys(context.organizations).length > 0) {
    prompt += '\n## 相关组织\n';
    Object.entries(context.organizations).forEach(([id, organization]) => {
      for (const key of Object.keys(organization)) {
        if (key !== 'name') {
          prompt += `- ${organization.name}的${key}: ${organization[key]}\n`;
        }
      }
      prompt += '\n';
    });
  }

  // 添加场景描述
  if (sceneData.description) {
    prompt += '\n## 场景描述\n';
    prompt += sceneData.description + '\n';
  }

  // 添加世界观背景
  if (context.worldBible) {
    prompt += '\n## 世界观背景\n';
    const worldBibleSummary = context.worldBible;
    prompt += worldBibleSummary + '\n';
  }

  // 添加分卷细纲
  if (context.volumeOutline) {
    prompt += '\n## 当前分卷细纲\n';
    prompt += context.volumeOutline + '\n';
  }

  // 添加伏笔处理指令
  if (context.foreshadowing.payoffOpportunities.length > 0) {
    prompt += '\n## 伏笔回收机会\n';
    prompt += '请在本章中自然地回收以下伏笔（选择1-2个最合适的）：\n';
    context.foreshadowing.payoffOpportunities.slice(0, 3).forEach((opportunity, index) => {
      prompt += `### 伏笔 ${index + 1}（优先级: ${opportunity.priority.toFixed(2)}）\n`;
      prompt += `- 伏笔类型: ${opportunity.foreshadowing.type}\n`;
      prompt += `- 伏笔内容: ${opportunity.foreshadowing.description}\n`;
      prompt += `- 回收建议: ${opportunity.payoffSuggestion}\n\n`;
    });
  }

  if (context.foreshadowing.suggestions && Object.keys(context.foreshadowing.suggestions).length > 0) {
    prompt += '\n## 新伏笔埋设建议\n';
    prompt += '可以考虑在本章中埋设以下伏笔（选择性使用）：\n';
    
    if (context.foreshadowing.suggestions.short_term && context.foreshadowing.suggestions.short_term.length > 0) {
      prompt += '### 短期伏笔\n';
      context.foreshadowing.suggestions.short_term.slice(0, 2).forEach(suggestion => {
        prompt += `- ${suggestion.description}\n`;
        prompt += `  实施建议: ${suggestion.implementation_hints.join('、')}\n`;
      });
    }
    
    if (context.foreshadowing.suggestions.medium_term && context.foreshadowing.suggestions.medium_term.length > 0) {
      prompt += '### 中期伏笔\n';
      context.foreshadowing.suggestions.medium_term.slice(0, 1).forEach(suggestion => {
        prompt += `- ${suggestion.description}\n`;
        prompt += `  实施建议: ${suggestion.implementation_hints.join('、')}\n`;
      });
    }
  }

  // 添加前置场景上下文
  if (context.previousScenes.length > 0) {
    prompt += '\n## 前置场景参考\n';
    context.previousScenes.forEach((scene, index) => {
      prompt += `### 场景 ${index + 1}\n`;
      prompt += scene.content + '\n\n';
    });
  }

  prompt += `
## 写作要求
1. 生成1500字左右的章节文本
2. 保持角色性格一致性，严格遵循角色已建立的性格设定
3. 体现场景目标中的要求，不要升华主题、不要套路化
4. 严格按照分卷细纲的规划推进剧情，不要偏离既定方向
5. 优先处理上述"伏笔回收机会"中优先级最高的1-2个伏笔
6. 适当埋设"新伏笔埋设建议"中的短期伏笔，保持自然
7. 使用生动的描写和口语化的对话，文笔风格使用男频网络小说风格（起点、晋江等）
8. 符合已建立的世界观设定，主角也会遇到困难和绝境
9. 为后续情节发展留下合适的铺垫，但不要过于明显
10. 世界观是用于编织剧情，不能提前透露，具体透露多少根据当前处于的主线剧情决定
11. 减少繁琐的环境描写、侧面描写和过度形容词修饰，更贴近网文风格
12. 适当添加幽默元素和爽点，保持读者兴趣
13. 确保章节结尾有足够的悬念或钩子，吸引读者继续阅读

请开始创作这个场景：`;

  return prompt;
}

// API配置函数已移至 api-config-utils.js

/**
 * 构建增强版系统消息
 */
function buildEnhancedSystemMessage() {
  return `你是一个专业的网络小说创作和分析助手。你需要完成以下任务：

1. **内容生成**：根据场景信息生成高质量的网文章节内容，少使用直接的心理描写，不要升华主题、不要套路化，适当添加幽默元素。
2. **角色状态分析**：分析章节内容中角色状态的变化
3. **伏笔识别**：识别章节中新埋设的伏笔
4. **关键事件提取**：提取章节中的关键情节点
5. **章节摘要**：生成简洁的章节摘要

请按照指定的JSON格式返回结果，确保所有部分都完整且准确。`;
}

/**
 * 构建增强版场景生成Prompt
 */
function buildEnhancedScenePrompt(context, sceneData, config) {
  // 复用原有的prompt构建逻辑
  let basePrompt = buildScenePrompt(context, sceneData);
  
  // 添加合并分析的要求
  basePrompt += `

## 合并分析要求

除了生成章节内容外，还需要同时进行以下分析：

### 1. 角色状态更新分析
分析本章中每个参与角色的状态变化：
- 情绪状态变化
- 位置变化  
- 当前状态描述更新
- 能力/物品的获得或失去
- 重要关系的变化

### 2. 伏笔识别分析
识别本章中新埋设的伏笔：
- 角色秘密的暗示
- 物品/能力的线索
- 未来事件的预兆
- 世界观扩展的提示
- 关系发展的暗示

### 3. 关键事件提取
提取本章的重要情节点：
- 主要冲突事件
- 重要对话内容
- 关键决策时刻
- 情节转折点
- 爽点设计

### 4. 章节摘要生成
生成50-100字的章节摘要，包含：
- 主要事件概述
- 角色发展要点
- 情节推进情况

## 输出格式要求

请按以下JSON格式返回结果：

\`\`\`json
{
  "chapter_content": "完整的章节文本内容...",
  "character_states": {
    "角色名称1": {
      "emotion": "新的情绪状态",
      "location": "新的位置",
      "current_status": "当前状态描述",
      "inventory_changes": {
        "added": ["新增的物品/能力"],
        "removed": ["失去的物品/能力"]
      },
      "relationship_changes": {
        "角色名": "关系变化描述"
      }
    }
  },
  "new_foreshadowing": [
    {
      "type": "character_secret",
      "description": "伏笔描述",
      "hints": ["具体的暗示文本"],
      "importance": 7,
      "estimated_payoff_distance": 3,
      "relevant_characters": ["相关角色"],
      "relevant_locations": ["相关地点"]
    }
  ],
  "key_events": [
    {
      "event_type": "conflict",
      "description": "事件描述",
      "participants": ["参与角色"],
      "significance": "high"
    }
  ],
  "chapter_summary": "简洁的章节摘要..."
}
\`\`\`

注意：
1. 章节内容必须完整且符合所有创作要求
2. 分析结果必须基于生成的章节内容
3. 确保JSON格式正确，所有字段都包含有意义的值
4. 如果某个角色没有状态变化，可以省略该角色
5. 如果没有新伏笔，new_foreshadowing数组可以为空`;

  return basePrompt;
}

/**
 * 解析增强版AI响应
 */
async function parseEnhancedResponse(aiResponse, projectPath, sceneData) {
  try {
    // 提取JSON部分
    let jsonContent = aiResponse;
    const jsonMatch = aiResponse.match(/```json\n([\s\S]*?)\n```/) ||
                     aiResponse.match(/```\n([\s\S]*?)\n```/) ||
                     aiResponse.match(/\{[\s\S]*\}/);

    if (jsonMatch) {
      jsonContent = jsonMatch[1] || jsonMatch[0];
    }

    const parsedData = JSON.parse(jsonContent);

    // 1. 保存章节内容
    const draftPath = path.join(projectPath, '3_manuscript', 'drafts', `${sceneData.scene_id}.md`);
    await fs.writeFile(draftPath, parsedData.chapter_content, 'utf8');

    // 2. 更新角色状态
    if (parsedData.character_states) {
      await updateCharacterStatesFromParsed(projectPath, parsedData.character_states);
    }

    // 3. 保存伏笔信息
    if (parsedData.new_foreshadowing && parsedData.new_foreshadowing.length > 0) {
      await saveForeshadowingFromParsed(projectPath, sceneData.scene_id, parsedData.new_foreshadowing);
    }

    // 4. 保存章节摘要
    if (parsedData.chapter_summary) {
      await saveChapterSummary(projectPath, sceneData.scene_id, {
        summary: parsedData.chapter_summary,
        key_events: parsedData.key_events || []
      });
    }

    return {
      success: true,
      draftPath,
      content: parsedData.chapter_content,
      combinedAnalysis: {
        character_updates: Object.keys(parsedData.character_states || {}).length,
        new_foreshadowing_count: (parsedData.new_foreshadowing || []).length,
        key_events_count: (parsedData.key_events || []).length,
        summary: parsedData.chapter_summary
      }
    };

  } catch (error) {
    console.error('解析增强响应失败:', error);
    
    // 降级处理：尝试提取纯文本内容
    const draftPath = path.join(projectPath, '3_manuscript', 'drafts', `${sceneData.scene_id}.md`);
    
    // 如果有文本内容，至少保存文本部分
    let textContent = aiResponse;
    const textMatch = aiResponse.match(/"chapter_content":\s*"([^"]*(?:\\.[^"]*)*)"/);
    if (textMatch) {
      textContent = textMatch[1].replace(/\\n/g, '\n').replace(/\\"/g, '"');
    }
    
    await fs.writeFile(draftPath, textContent, 'utf8');
    
    return {
      success: true,
      draftPath,
      content: textContent,
      warning: '合并分析解析失败，已保存文本内容',
      error: error.message
    };
  }
}

/**
 * 更新角色状态（从解析数据）
 */
async function updateCharacterStatesFromParsed(projectPath, characterStates) {
  for (const [characterName, updates] of Object.entries(characterStates)) {
    try {
      const charactersDir = path.join(projectPath, '1_knowledge_base', 'characters');
      const characterFiles = await fs.readdir(charactersDir);
      
      for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
        const filePath = path.join(charactersDir, file);
        const fileContent = await fs.readFile(filePath, 'utf8');
        const characterData = yaml.load(fileContent);
        
        if (characterData.name === characterName) {
          // 更新状态
          if (!characterData.state) characterData.state = {};
          
          if (updates.emotion) characterData.state.emotion = updates.emotion;
          if (updates.location) characterData.state.location = updates.location;
          if (updates.current_status) characterData.state.current_status = updates.current_status;
          
          // 处理物品变化
          if (updates.inventory_changes) {
            if (!characterData.state.inventory) characterData.state.inventory = [];
            
            if (updates.inventory_changes.added) {
              characterData.state.inventory.push(...updates.inventory_changes.added);
            }
            if (updates.inventory_changes.removed) {
              characterData.state.inventory = characterData.state.inventory.filter(
                item => !updates.inventory_changes.removed.includes(item)
              );
            }
          }
          
          // 处理关系变化
          if (updates.relationship_changes) {
            if (!characterData.relationships) characterData.relationships = {};
            Object.assign(characterData.relationships, updates.relationship_changes);
          }
          
          await fs.writeFile(filePath, yaml.dump(characterData, { indent: 2 }), 'utf8');
          break;
        }
      }
    } catch (error) {
      console.error(`更新角色${characterName}状态失败:`, error);
    }
  }
}

/**
 * 保存伏笔信息（从解析数据）
 */
async function saveForeshadowingFromParsed(projectPath, sceneId, newForeshadowing) {
  try {
    const foreshadowingPath = path.join(projectPath, '5_plot_devices', 'foreshadowing.yml');
    
    let foreshadowingData;
    try {
      const content = await fs.readFile(foreshadowingPath, 'utf8');
      foreshadowingData = yaml.load(content);
    } catch (e) {
      foreshadowingData = {
        metadata: { created_at: new Date().toISOString() },
        active_foreshadowing: [],
        resolved_foreshadowing: []
      };
    }

    for (const foreshadowing of newForeshadowing) {
      const newItem = {
        id: `foreshadowing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        planted_in_scene: sceneId,
        planted_at: new Date().toISOString(),
        type: foreshadowing.type,
        description: foreshadowing.description,
        hints: foreshadowing.hints || [],
        importance: foreshadowing.importance || 5,
        estimated_payoff_distance: foreshadowing.estimated_payoff_distance || 3,
        relevant_characters: foreshadowing.relevant_characters || [],
        relevant_locations: foreshadowing.relevant_locations || [],
        tags: foreshadowing.tags || [],
        status: 'active'
      };
      
      foreshadowingData.active_foreshadowing.push(newItem);
    }

    foreshadowingData.metadata.last_updated = new Date().toISOString();
    await fs.writeFile(foreshadowingPath, yaml.dump(foreshadowingData, { indent: 2 }), 'utf8');
    
  } catch (error) {
    console.error('保存伏笔信息失败:', error);
  }
}

/**
 * 保存章节摘要
 */
async function saveChapterSummary(projectPath, sceneId, summaryData) {
  try {
    const summaryPath = path.join(projectPath, '4_summaries', `${sceneId}.yml`);
    await fs.writeFile(summaryPath, yaml.dump(summaryData, { indent: 2 }), 'utf8');
  } catch (error) {
    console.error('保存章节摘要失败:', error);
  }
}

module.exports = {
  generateSceneDraft,
  compileSceneContext,
  buildScenePrompt,
  buildEnhancedScenePrompt,
  parseEnhancedResponse
};

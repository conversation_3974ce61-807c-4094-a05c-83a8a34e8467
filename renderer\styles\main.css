/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f5;
    overflow: hidden;
}

/* 应用主容器 */
#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部标题栏 */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    -webkit-app-region: drag;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.app-title {
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.app-icon {
    font-size: 20px;
}

.project-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 12px;
    opacity: 0.9;
}

.project-name {
    font-weight: 500;
}

.project-path {
    opacity: 0.7;
    font-size: 11px;
}

/* 主要内容区域 */
.app-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧导航栏 */
.sidebar {
    width: 240px;
    background: #2c3e50;
    color: white;
    overflow-y: auto;
    padding: 20px 0;
}

.nav-section {
    margin-bottom: 24px;
}

.nav-title {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #bdc3c7;
    padding: 0 20px 8px;
    border-bottom: 1px solid #34495e;
    margin-bottom: 12px;
}

.nav-list {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
}

.nav-item:hover {
    background: #34495e;
}

.nav-item.active {
    background: #3498db;
    border-right: 3px solid #2980b9;
}

.nav-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* 主内容区域 */
.content-area {
    flex: 1;
    background: white;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.view-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.view-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e8ed;
    background: white;
}

.view-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
}

.view-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* 仪表板样式 */
.dashboard-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.welcome-section {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.welcome-card {
    text-align: center;
    padding: 48px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.welcome-card h3 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
}

.welcome-card p {
    color: #7f8c8d;
    margin-bottom: 32px;
    font-size: 16px;
}

.action-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 16px;
}

/* 项目统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-top: 24px;
}

.stat-card {
    background: white;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
}

/* 占位符文本 */
.placeholder-text {
    text-align: center;
    color: #7f8c8d;
    font-size: 16px;
    margin-top: 48px;
}

/* 状态栏 */
.status-bar {
    height: 24px;
    background: #ecf0f1;
    border-top: 1px solid #bdc3c7;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    font-size: 12px;
    color: #7f8c8d;
}

.status-left,
.status-right {
    display: flex;
    gap: 16px;
}

.status-item {
    display: flex;
    align-items: center;
}

/* 知识库管理样式 */
.view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.empty-state {
    text-align: center;
    padding: 48px;
    color: #7f8c8d;
}

.empty-state p {
    margin-bottom: 24px;
    font-size: 16px;
}

/* 角色卡片网格 */
.characters-grid,
.locations-grid,
.organizations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.character-card,
.location-card,
.organization-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.character-card:hover,
.location-card:hover,
.organization-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.character-header,
.location-header,
.organization-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.character-name,
.location-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.character-actions,
.location-actions {
    display: flex;
    gap: 8px;
}

.btn-icon-small {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.btn-icon-small:hover {
    background: #f8f9fa;
}

.character-info,
.location-info {
    color: #7f8c8d;
    font-size: 13px;
}

.character-description,
.character-motivation,
.character-current-status,
.location-description,
.location-type {
    margin-bottom: 8px;
    line-height: 1.4;
}

.character-state {
    display: flex;
    gap: 12px;
    margin-top: 12px;
}

.state-item {
    background: #ecf0f1;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #2c3e50;
}

/* 组织特有样式 */
.organization-info {
    flex: 1;
}

.organization-name {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.organization-type {
    background: #8e44ad;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.organization-details {
    color: #7f8c8d;
    font-size: 13px;
}

.organization-details > div {
    margin-bottom: 8px;
    line-height: 1.4;
}

.organization-details strong {
    color: #2c3e50;
    margin-right: 8px;
}

.influence-level {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.influence-local {
    background: #95a5a6;
    color: white;
}

.influence-regional {
    background: #3498db;
    color: white;
}

.influence-national {
    background: #e67e22;
    color: white;
}

.influence-international {
    background: #e74c3c;
    color: white;
}

.influence-legendary {
    background: #9b59b6;
    color: white;
}

.influence-unknown {
    background: #bdc3c7;
    color: #2c3e50;
}

.status-badge {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.status-active {
    background: #27ae60;
    color: white;
}

.status-dormant {
    background: #95a5a6;
    color: white;
}

.status-declining {
    background: #e67e22;
    color: white;
}

.status-rising {
    background: #f39c12;
    color: white;
}

.status-disbanded {
    background: #e74c3c;
    color: white;
}

.status-hidden {
    background: #34495e;
    color: white;
}

.status-unknown {
    background: #bdc3c7;
    color: #2c3e50;
}

/* 场景管理样式 */
.scenes-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px 0;
}

.scene-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.scene-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.scene-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.scene-info {
    flex: 1;
}

.scene-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.scene-storyline {
    background: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.scene-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
}

.btn-success:disabled {
    background: #95a5a6;
    cursor: not-allowed;
}

.scene-details {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
}

.scene-details > div {
    margin-bottom: 8px;
}

.scene-details strong {
    color: #2c3e50;
    margin-right: 8px;
}

.text-muted {
    color: #bdc3c7;
    font-style: italic;
}

.goals-list {
    margin: 8px 0 0 16px;
    padding: 0;
}

.goals-list li {
    margin-bottom: 4px;
}

.goal-type {
    font-weight: 500;
    color: #8e44ad;
}

.scene-description p {
    margin: 8px 0 0 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #3498db;
}

/* 伏笔管理样式 */
.foreshadowing-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.foreshadowing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.foreshadowing-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e1e8ed;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: #7f8c8d;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #3498db;
}

.tab-btn.active {
    color: #3498db;
    border-bottom-color: #3498db;
}

.foreshadowing-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.foreshadowing-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 4px solid #3498db;
}

.foreshadowing-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.foreshadowing-card.resolved {
    border-left-color: #27ae60;
    opacity: 0.8;
}

.foreshadowing-card.planned {
    border-left-color: #f39c12;
}

.foreshadowing-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.foreshadowing-info {
    flex: 1;
}

.foreshadowing-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.foreshadowing-meta {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.foreshadowing-type {
    background: #3498db;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.foreshadowing-importance {
    background: #e74c3c;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.foreshadowing-distance {
    background: #f39c12;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.resolved-badge {
    background: #27ae60;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.planned-badge {
    background: #f39c12;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.foreshadowing-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.foreshadowing-details {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
}

.foreshadowing-details > div {
    margin-bottom: 8px;
}

.foreshadowing-details strong {
    color: #2c3e50;
    margin-right: 8px;
}

.hints-list {
    margin: 8px 0 0 16px;
    padding: 0;
}

.hints-list li {
    margin-bottom: 4px;
    color: #5d6d7e;
}

.foreshadowing-tags {
    margin-top: 8px;
}

.tag {
    display: inline-block;
    background: #ecf0f1;
    color: #2c3e50;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 6px;
    margin-bottom: 4px;
}

.foreshadowing-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border-left: 3px solid #3498db;
}

.foreshadowing-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
}

.foreshadowing-info .text-muted {
    color: #7f8c8d;
    font-size: 14px;
}

/* API配置样式 */
.api-config-section {
    margin-top: 32px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.api-config-section h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 18px;
}

.config-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    font-size: 14px;
}

.status-ok {
    color: #27ae60;
}

.status-warning {
    color: #f39c12;
}

/* 表单帮助文本 */
.form-help {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #7f8c8d;
}

/* 范围输入样式 */
.form-group input[type="range"] {
    width: 100%;
    margin: 8px 0;
}

.range-value {
    text-align: center;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 4px;
}

/* 模型选择按钮 */
.model-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.model-buttons .btn {
    font-size: 12px;
    padding: 6px 12px;
}

/* 世界观设定和大纲编辑器样式 */
.world-bible-editor,
.outline-editor {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.world-bible-textarea,
.outline-textarea {
    flex: 1;
    border: none;
    outline: none;
    padding: 20px;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-size: 16px;
    line-height: 1.8;
    resize: none;
    background: white;
}

.world-bible-textarea::placeholder,
.outline-textarea::placeholder {
    color: #bdc3c7;
    font-style: italic;
}

/* 工具栏按钮特殊样式 */
.toolbar-btn {
    white-space: nowrap;
    font-size: 13px;
}

/* 编辑器特定的工具栏 */
.world-bible-editor .editor-toolbar,
.outline-editor .editor-toolbar {
    flex-wrap: wrap;
    gap: 6px;
    padding: 12px 16px;
}

/* 生成状态样式 */
.generating-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.generating-dialog {
    background: white;
    padding: 32px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.generating-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.generating-text {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 8px;
}

.generating-subtext {
    font-size: 14px;
    color: #7f8c8d;
}

/* 草稿和已发布列表样式 */
.drafts-list,
.published-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px 0;
}

.draft-card,
.published-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.draft-card:hover,
.published-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.draft-header,
.published-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.draft-title,
.published-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 4px 0;
}

.draft-meta,
.published-meta {
    font-size: 12px;
    color: #7f8c8d;
}

.draft-actions,
.published-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.draft-preview,
.published-preview {
    color: #7f8c8d;
    font-size: 14px;
    line-height: 1.5;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 文本编辑器样式 */
.editor-overlay {
    z-index: 1500;
}

.editor-dialog {
    width: 95%;
    max-width: 1000px;
    height: 90vh;
    max-height: none;
    display: flex;
    flex-direction: column;
}

.editor-dialog .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #e1e8ed;
    flex-shrink: 0;
}

.editor-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.editor-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
}

.editor-toolbar {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e8ed;
    background: #f8f9fa;
    flex-shrink: 0;
}

.toolbar-btn {
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 10px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.toolbar-btn:active {
    background: #dee2e6;
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background: #ddd;
    margin: 0 8px;
}

.text-editor {
    flex: 1;
    border: none;
    outline: none;
    padding: 20px;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-size: 16px;
    line-height: 1.8;
    resize: none;
    background: white;
}

.editor-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e1e8ed;
    font-size: 12px;
    flex-shrink: 0;
}

.word-count {
    color: #6c757d;
}

.save-status {
    font-weight: 500;
}

.save-status.saved {
    color: #28a745;
}

.save-status.unsaved {
    color: #ffc107;
}

/* 通知系统样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    max-width: 500px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    z-index: 3000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.notification.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.notification.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.notification.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification-icon {
    font-size: 18px;
    flex-shrink: 0;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: opacity 0.2s ease;
}

.notification-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 加载状态样式 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }

    .modal-dialog {
        width: 95%;
        margin: 10px;
    }

    .editor-dialog {
        width: 98%;
        height: 95vh;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* 改进的按钮状态 */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
}

/* 改进的表单验证 */
.form-group.error input,
.form-group.error textarea,
.form-group.error select {
    border-color: #e74c3c;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2);
}

.form-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* 模态对话框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-large {
    max-width: 700px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e1e8ed;
    background: #f8f9fa;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background: #e9ecef;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e1e8ed;
    background: #f8f9fa;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 复选框组样式 */
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 8px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: normal;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.checkbox-label:hover {
    background: #f8f9fa;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* 目标编辑器样式 */
.goals-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    background: #f8f9fa;
}

.goal-item {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
}

.goal-type-select {
    width: 120px;
    flex-shrink: 0;
}

.goal-content {
    flex: 1;
}

.btn-remove {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn-remove:hover {
    background: #c0392b;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* API配置标签页样式 */
.api-config-tabs {
    margin-bottom: 20px;
}

.tab-header {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 20px;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #333;
    background-color: #f8f9fa;
}

.tab-button.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background-color: #f8f9fa;
}

.tab-content {
    animation: fadeIn 0.3s ease;
}

.tab-content h4 {
    color: #333;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 600;
}

.tab-description {
    color: #666;
    font-size: 13px;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 3px solid #667eea;
    border-radius: 4px;
}

.modal-large {
    max-width: 700px;
    width: 90%;
}

.form-note {
    background-color: #e8f4fd;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 12px;
    margin-top: 20px;
    font-size: 13px;
    color: #0066cc;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* AI生成标签样式 */
.ai-tag {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 10px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    vertical-align: middle;
    animation: aiGlow 2s ease-in-out infinite alternate;
}

@keyframes aiGlow {
    from {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    to {
        box-shadow: 0 0 10px rgba(102, 126, 234, 0.8);
    }
}

/* ========== 工作流程管理样式 ========== */

/* 工作流程区域 */
.workflow-section {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.workflow-section h3 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.workflow-section h3::before {
    content: "🚀";
    font-size: 20px;
}

/* 工作流程状态 */
.workflow-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.workflow-info {
    flex: 1;
}

.workflow-phase {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 8px;
}

.workflow-progress {
    color: #6c757d;
    font-size: 13px;
}

.progress-text {
    display: block;
    margin-top: 4px;
}

/* 工作流程操作按钮 */
.workflow-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.volume-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 工作流程配置对话框样式 */
.modal-xlarge {
    max-width: 900px;
    width: 95%;
}

.workflow-calculation {
    background: #e8f4fd;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 16px;
    margin: 20px 0;
}

.workflow-calculation h4 {
    margin: 0 0 12px 0;
    color: #0066cc;
    font-size: 16px;
}

.calculation-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.calc-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #cce7ff;
}

.calc-label {
    font-weight: 500;
    color: #333;
}

.calc-value {
    font-weight: 600;
    color: #0066cc;
    font-size: 16px;
}

/* 大纲和分卷审阅样式 */
.outline-preview,
.volume-preview {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
}

.outline-content,
.volume-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #2c3e50;
    margin: 0;
}

.outline-editor,
.volume-editor {
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    overflow: hidden;
}

.outline-textarea,
.volume-textarea {
    width: 100%;
    min-height: 400px;
    border: none;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    resize: vertical;
    background: white;
}

.outline-textarea:focus,
.volume-textarea:focus {
    outline: none;
    box-shadow: inset 0 0 0 2px #667eea;
}

/* 分卷细纲标签页 */
.volume-outline-tabs {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.volume-tab {
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    overflow: hidden;
}

.volume-tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e8ed;
}

.volume-tab-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.volume-tab-content {
    padding: 20px;
}

/* 工作流程配置项 */
.workflow-config {
    background: #f8f9fa;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 16px;
    margin: 16px 0;
}

.workflow-config h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 14px;
    font-weight: 600;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #e1e8ed;
}

.config-item:last-child {
    border-bottom: none;
}

.config-label {
    color: #6c757d;
    font-size: 13px;
}

.config-value {
    font-weight: 600;
    color: #2c3e50;
    font-size: 13px;
}

/* 工作流程警告 */
.workflow-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 16px;
    margin: 16px 0;
}

.workflow-warning p {
    margin: 0 0 8px 0;
    color: #856404;
    font-weight: 500;
}

.workflow-warning ul {
    margin: 8px 0 0 20px;
    color: #856404;
}

.workflow-warning li {
    margin-bottom: 4px;
    font-size: 13px;
}

/* 模态对话框动作区域 */
.modal-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.modal-header .modal-actions {
    margin-right: 12px;
}

/* 响应式工作流程界面 */
@media (max-width: 768px) {
    .workflow-status {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .workflow-actions,
    .volume-buttons {
        flex-direction: column;
    }
    
    .calculation-grid {
        grid-template-columns: 1fr;
    }
    
    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .volume-tab-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
}

/* 新的API配置界面样式 */
.api-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.api-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #667eea;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.api-form-content {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.api-disabled-notice {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 6px;
    text-align: center;
    color: #6c757d;
}

.api-status-summary {
    margin-top: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.api-status-summary h5 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.status-name {
    font-size: 12px;
    color: #495057;
}

.status-badge {
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status-badge.success {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.error {
    background-color: #f8d7da;
    color: #721c24;
}

.status-badge.disabled {
    background-color: #e2e3e5;
    color: #6c757d;
}

/* 响应式API配置界面 */
@media (max-width: 768px) {
    .status-grid {
        grid-template-columns: 1fr;
    }

    .api-config-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

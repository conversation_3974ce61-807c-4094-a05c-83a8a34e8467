// Vue应用主逻辑
const { createApp } = Vue;

createApp({
    data() {
        return {
            // 当前激活的视图
            activeView: 'dashboard',
            
            // 当前项目信息
            currentProject: null,
            
            // 工作流程管理状态
            workflowStatus: null,
            showWorkflowDialog: false,
            showOutlineReviewDialog: false,
            showVolumeReviewDialog: false,
            showVolumeWritingDialog: false,
            selectedVolume: null,
            
            // 工作流程配置表单
            workflowForm: {
                genre: '',
                theme: '',
                style: '现代网文风格',
                special_requirements: '',
                target_total_words: 200000,
                average_chapter_words: 2000,
                target_volumes: 4,
                chapters_per_batch: 5,
                pause_between_volumes: true
            },

            // 编辑状态
            outlineEditMode: false,
            volumeEditMode: {},
            editedOutlineContent: '',
            editedVolumeOutlines: {},
            
            // 项目统计数据
            projectStats: {
                characters: 0,
                locations: 0,
                organizations: 0,
                scenes: 0,
                drafts: 0,
                published: 0
            },

            // 知识库数据
            characters: [],
            locations: [],
            organizations: [],
            scenes: [],
            drafts: [],
            publishedChapters: [],

            // 伏笔管理数据
            activeForeshadowing: [],
            resolvedForeshadowing: [],
            plannedForeshadowing: [],
            activeForeshadowingTab: 'active',

            // 对话框状态
            showCharacterDialog: false,
            showLocationDialog: false,
            showOrganizationDialog: false,
            showSceneDialog: false,
            showAiSceneDialog: false,
            showApiDialog: false,
            activeApiTab: 'primary_generation', // 当前激活的API配置标签页
            showEditorDialog: false,
            showForeshadowingDialog: false,
            showForeshadowingResolveDialog: false,
            editingCharacter: null,
            editingLocation: null,
            editingOrganization: null,
            editingScene: null,
            editingDraft: null,
            editingForeshadowing: null,
            resolvingForeshadowing: null,

            // API配置状态
            apiConfigured: false,
            isGenerating: false,

            // 编辑器状态
            editorContent: '',
            isSaved: true,
            autoSaveTimer: null,

            // 生成状态文本
            generatingText: 'AI正在工作...',
            generatingSubtext: '请耐心等待',

            // 通知系统
            notification: {
                show: false,
                type: 'info', // success, error, warning, info
                icon: 'ℹ️',
                message: '',
                timer: null
            },

            // 世界观设定
            worldBibleContent: '',
            worldBibleChanged: false,
            worldBibleAutoSaveTimer: null,

            // 主线大纲
            outlineContent: '',
            outlineChanged: false,
            outlineAutoSaveTimer: null,

            // 表单数据
            characterForm: {
                name: '',
                description: '',
                motivation: '',
                appearance: '',
                emotion: '',
                location: '',
                current_status: ''
            },
            locationForm: {
                name: '',
                type: '',
                description: '',
                atmosphere: ''
            },
            organizationForm: {
                name: '',
                type: '',
                description: '',
                leader: '',
                members: [],
                influence_level: '',
                status: '',
                headquarters: '',
                resources: ''
            },
            sceneForm: {
                scene_id: '',
                storyline: 'main',
                characters: [],
                location: '',
                goals: '',
                description: ''
            },
            // AI创建场景的参数表单
            aiSceneForm: {
                scene_id: '',
                characters: [],
                excludeCharacters: [],
                location: '',
                goals: '',
                description: ''
            },
            // 伏笔表单数据
            foreshadowingForm: {
                type: 'character_secret',
                description: '',
                hints: [],
                importance: 5,
                estimated_payoff_distance: 3,
                tags: [],
                relevant_characters: [],
                relevant_locations: [],
                planted_in_scene: '',
                implementation_notes: ''
            },
            // 伏笔回收表单数据
            foreshadowingResolveForm: {
                resolved_in_scene: '',
                resolution_details: ''
            },
            // API配置表单数据 - 新的ai_apis格式
            aiApisForm: {
                primary_generation: {
                    enabled: true,
                    base_url: 'https://api.openai.com/v1',
                    api_key: '',
                    model: 'gpt-3.5-turbo',
                    temperature: 0.7,
                    description: '主要生成API - 用于场景初稿生成'
                },
                analysis: {
                    enabled: true,
                    base_url: 'https://api.openai.com/v1',
                    api_key: '',
                    model: 'gpt-3.5-turbo',
                    temperature: 0.3,
                    description: '分析API - 用于场景分析和状态更新'
                },
                secondary_generation: {
                    enabled: false,
                    base_url: 'https://api.openai.com/v1',
                    api_key: '',
                    model: 'gpt-3.5-turbo',
                    temperature: 0.8,
                    description: '辅助生成API - 用于内容优化和重写'
                },
                planning: {
                    enabled: false,
                    base_url: 'https://api.openai.com/v1',
                    api_key: '',
                    model: 'gpt-4',
                    temperature: 0.5,
                    description: '规划API - 用于大纲生成和情节规划'
                }
            },
            // API类型定义
            apiTypes: {
                primary_generation: {
                    title: '主要生成API',
                    description: '用于场景初稿生成，可以使用较高的temperature值以增加创造性',
                    defaultModel: 'gpt-3.5-turbo',
                    temperatureHelp: '生成建议使用中等到较高值(0.5-0.8)增加创造性'
                },
                analysis: {
                    title: '分析API',
                    description: '用于场景分析和角色状态更新，建议使用较低的temperature值以确保分析结果的稳定性',
                    defaultModel: 'gpt-3.5-turbo',
                    temperatureHelp: '分析建议使用较低值(0.1-0.3)确保结果稳定'
                },
                secondary_generation: {
                    title: '辅助生成API',
                    description: '用于内容优化和重写，可以使用更高的创造性参数',
                    defaultModel: 'gpt-3.5-turbo',
                    temperatureHelp: '辅助生成可以使用较高值(0.6-0.9)增加多样性'
                },
                planning: {
                    title: '规划API',
                    description: '用于大纲生成和情节规划，建议使用更强大的模型',
                    defaultModel: 'gpt-4',
                    temperatureHelp: '规划建议使用中等值(0.4-0.6)平衡创造性和逻辑性'
                }
            },
            // 向后兼容的API配置表单数据
            analysisApiForm: {
                base_url: 'https://api.openai.com/v1',
                api_key: '',
                model: 'gpt-3.5-turbo',
                temperature: 0.3
            },
            generationApiForm: {
                base_url: 'https://api.openai.com/v1',
                api_key: '',
                model: 'gpt-3.5-turbo',
                temperature: 0.7
            }
        };
    },

    computed: {
        wordCount() {
            return this.editorContent.length;
        },
        worldBibleWordCount() {
            return this.worldBibleContent.length;
        },
        outlineWordCount() {
            return this.outlineContent.length;
        },

        // 伏笔表单的计算属性
        hintsText: {
            get() {
                return this.foreshadowingForm.hints.join('\n');
            },
            set(value) {
                this.foreshadowingForm.hints = value.split('\n').filter(h => h.trim());
            }
        },
        charactersText: {
            get() {
                return this.foreshadowingForm.relevant_characters.join(', ');
            },
            set(value) {
                this.foreshadowingForm.relevant_characters = value.split(',').map(c => c.trim()).filter(c => c);
            }
        },
        locationsText: {
            get() {
                return this.foreshadowingForm.relevant_locations.join(', ');
            },
            set(value) {
                this.foreshadowingForm.relevant_locations = value.split(',').map(l => l.trim()).filter(l => l);
            }
        },
        tagsText: {
            get() {
                return this.foreshadowingForm.tags.join(', ');
            },
            set(value) {
                this.foreshadowingForm.tags = value.split(',').map(t => t.trim()).filter(t => t);
            }
        }
    },

    methods: {
        // 设置激活视图
        setActiveView(view) {
            this.activeView = view;

            // 根据视图加载相应数据
            if (view === 'foreshadowing' && this.currentProject) {
                this.loadForeshadowingData();
            }
        },

        // 通知系统方法
        showNotification(message, type = 'info', duration = 5000) {
            // 清除之前的定时器
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
            }

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            this.notification = {
                show: true,
                type,
                icon: icons[type] || icons.info,
                message,
                timer: null
            };

            // 自动隐藏
            if (duration > 0) {
                this.notification.timer = setTimeout(() => {
                    this.hideNotification();
                }, duration);
            }
        },

        hideNotification() {
            this.notification.show = false;
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
                this.notification.timer = null;
            }
        },
        
        // 获取视图标题
        getViewTitle() {
            const titles = {
                dashboard: '项目概览',
                characters: '角色管理',
                locations: '地点管理',
                organizations: '组织管理',
                worldbible: '世界观设定',
                outline: '主线大纲',
                scenes: '场景卡片',
                drafts: '草稿箱',
                published: '已发布'
            };
            return titles[this.activeView] || '未知视图';
        },
        
        // 创建新项目
        async createNewProject() {
            try {
                const result = await window.electronAPI.showSaveDialog({
                    title: '选择项目保存位置',
                    defaultPath: 'my_novel',
                    properties: ['createDirectory']
                });
                
                if (!result.canceled && result.filePath) {
                    await this.initializeProject(result.filePath);
                }
            } catch (error) {
                console.error('创建项目失败:', error);
                this.showNotification('创建项目失败: ' + error.message, 'error');
            }
        },
        
        // 打开现有项目
        async openExistingProject() {
            try {
                const result = await window.electronAPI.showOpenDialog({
                    title: '选择项目文件夹',
                    properties: ['openDirectory']
                });
                
                if (!result.canceled && result.filePaths.length > 0) {
                    await this.loadProject(result.filePaths[0]);
                }
            } catch (error) {
                console.error('打开项目失败:', error);
                alert('打开项目失败: ' + error.message);
            }
        },
        
        // 初始化新项目
        async initializeProject(projectPath) {
            try {
                // 创建项目目录结构
                const directories = [
                    '1_knowledge_base/characters',
                    '1_knowledge_base/locations',
                    '1_knowledge_base/organizations',
                    '2_plot/scene_cards',
                    '3_manuscript/drafts',
                    '3_manuscript/published',
                    '4_summaries'
                ];
                
                for (const dir of directories) {
                    await window.electronAPI.createDirectory(`${projectPath}/${dir}`);
                }
                
                // 创建主配置文件
                const projectName = projectPath.split(/[/\\]/).pop();
                const config = {
                    name: projectName,
                    version: '1.0.0',
                    author: '',
                    description: '',
                    created_at: new Date().toISOString(),
                    // 新的AI APIs配置
                    ai_apis: {
                        primary_generation: {
                            enabled: true,
                            base_url: 'https://api.openai.com/v1',
                            api_key: '',
                            model: 'gpt-3.5-turbo',
                            temperature: 0.7,
                            description: '主要生成API - 用于场景初稿生成'
                        },
                        analysis: {
                            enabled: true,
                            base_url: 'https://api.openai.com/v1',
                            api_key: '',
                            model: 'gpt-3.5-turbo',
                            temperature: 0.3,
                            description: '分析API - 用于场景分析和状态更新'
                        },
                        secondary_generation: {
                            enabled: false,
                            base_url: 'https://api.openai.com/v1',
                            api_key: '',
                            model: 'gpt-3.5-turbo',
                            temperature: 0.8,
                            description: '辅助生成API - 用于内容优化和重写'
                        },
                        planning: {
                            enabled: false,
                            base_url: 'https://api.openai.com/v1',
                            api_key: '',
                            model: 'gpt-4',
                            temperature: 0.5,
                            description: '规划API - 用于大纲生成和情节规划'
                        }
                    },
                    // 向后兼容的API配置
                    analysis_api: {
                        base_url: 'https://api.openai.com/v1',
                        api_key: '',
                        model: 'gpt-3.5-turbo',
                        temperature: 0.3
                    },
                    generation_api: {
                        base_url: 'https://api.openai.com/v1',
                        api_key: '',
                        model: 'gpt-3.5-turbo',
                        temperature: 0.7
                    }
                };
                
                await window.electronAPI.writeYaml(`${projectPath}/chronicler.yml`, config);
                
                // 创建世界观设定模板
                const worldBibleTemplate = `# 世界观设定

## 基本设定

### 时代背景
- 时间：
- 地点：
- 社会制度：

### 物理法则
- 魔法系统：
- 科技水平：
- 特殊规则：

## 历史背景

### 重要事件
- 

### 传说故事
- 

## 文化设定

### 语言文字
- 

### 宗教信仰
- 

### 社会习俗
- 
`;
                
                await window.electronAPI.writeFile(`${projectPath}/1_knowledge_base/world_bible.md`, worldBibleTemplate);
                
                // 创建主线大纲模板
                const outlineTemplate = `# 主线大纲

## 故事概述
- 

## 主要角色
- 

## 情节结构

### 第一幕：开端
- 

### 第二幕：发展
- 

### 第三幕：高潮
- 

### 第四幕：结局
- 

## 主题思想
- 
`;
                
                await window.electronAPI.writeFile(`${projectPath}/2_plot/main_outline.md`, outlineTemplate);
                
                // 加载项目
                await this.loadProject(projectPath);

                this.showNotification('项目创建成功！', 'success');
            } catch (error) {
                console.error('初始化项目失败:', error);
                throw error;
            }
        },
        
        // 加载项目
        async loadProject(projectPath) {
            try {
                // 检查项目配置文件是否存在
                const configPath = `${projectPath}/chronicler.yml`;
                const configExists = await window.electronAPI.checkPathExists(configPath);
                
                if (!configExists.exists) {
                    throw new Error('这不是一个有效的Chronicler项目文件夹');
                }
                
                // 读取项目配置
                const configResult = await window.electronAPI.readYaml(configPath);
                if (!configResult.success) {
                    throw new Error('无法读取项目配置文件');
                }
                
                // 设置当前项目
                this.currentProject = {
                    name: configResult.data.name,
                    path: projectPath,
                    config: configResult.data
                };
                
                // 更新项目统计
                await this.updateProjectStats();

                // 加载知识库数据
                await this.loadCharacters();
                await this.loadLocations();
                await this.loadOrganizations();
                await this.loadScenes();
                await this.loadDrafts();
                await this.loadPublishedChapters();
                await this.loadWorldBible();
                await this.loadOutline();

                // 检查API配置
                this.checkApiConfiguration();
                
            } catch (error) {
                console.error('加载项目失败:', error);
                throw error;
            }
        },
        
        // 更新项目统计
        async updateProjectStats() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;

                // 统计角色数量
                const charactersResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/characters`);
                this.projectStats.characters = charactersResult.success ?
                    charactersResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计地点数量
                const locationsResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/locations`);
                this.projectStats.locations = locationsResult.success ?
                    locationsResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计组织数量
                const organizationsResult = await window.electronAPI.listDirectory(`${basePath}/1_knowledge_base/organizations`);
                this.projectStats.organizations = organizationsResult.success ?
                    organizationsResult.data.filter(f => f.isFile && f.name.endsWith('.yml')).length : 0;

                // 统计场景数量
                const scenesResult = await window.electronAPI.listDirectory(`${basePath}/2_plot/scene_cards`);
                this.projectStats.scenes = scenesResult.success ?
                    scenesResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

                // 统计草稿数量
                const draftsResult = await window.electronAPI.listDirectory(`${basePath}/3_manuscript/drafts`);
                this.projectStats.drafts = draftsResult.success ?
                    draftsResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

                // 统计已发布数量
                const publishedResult = await window.electronAPI.listDirectory(`${basePath}/3_manuscript/published`);
                this.projectStats.published = publishedResult.success ?
                    publishedResult.data.filter(f => f.isFile && f.name.endsWith('.md')).length : 0;

            } catch (error) {
                console.error('更新项目统计失败:', error);
            }
        },

        // 加载角色数据
        async loadCharacters() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const charactersDir = `${basePath}/1_knowledge_base/characters`;

                const result = await window.electronAPI.listDirectory(charactersDir);
                if (!result.success) {
                    this.characters = [];
                    return;
                }

                const characterFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const characters = [];

                for (const file of characterFiles) {
                    const filePath = `${charactersDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const character = yamlResult.data;
                        character.filename = file.name;
                        characters.push(character);
                    }
                }

                this.characters = characters;
            } catch (error) {
                console.error('加载角色数据失败:', error);
                this.characters = [];
            }
        },

        // 加载地点数据
        async loadLocations() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const locationsDir = `${basePath}/1_knowledge_base/locations`;

                const result = await window.electronAPI.listDirectory(locationsDir);
                if (!result.success) {
                    this.locations = [];
                    return;
                }

                const locationFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const locations = [];

                for (const file of locationFiles) {
                    const filePath = `${locationsDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const location = yamlResult.data;
                        location.filename = file.name;
                        locations.push(location);
                    }
                }

                this.locations = locations;
            } catch (error) {
                console.error('加载地点数据失败:', error);
                this.locations = [];
            }
        },

        // 加载组织数据
        async loadOrganizations() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const organizationsDir = `${basePath}/1_knowledge_base/organizations`;

                const result = await window.electronAPI.listDirectory(organizationsDir);
                if (!result.success) {
                    this.organizations = [];
                    return;
                }

                const organizationFiles = result.data.filter(f => f.isFile && f.name.endsWith('.yml'));
                const organizations = [];

                for (const file of organizationFiles) {
                    const filePath = `${organizationsDir}/${file.name}`;
                    const yamlResult = await window.electronAPI.readYaml(filePath);

                    if (yamlResult.success) {
                        const organization = yamlResult.data;
                        organization.filename = file.name;
                        organizations.push(organization);
                    }
                }

                this.organizations = organizations;
            } catch (error) {
                console.error('加载组织数据失败:', error);
                this.organizations = [];
            }
        },

        // 加载场景数据
        async loadScenes() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const scenesDir = `${basePath}/2_plot/scene_cards`;

                const result = await window.electronAPI.listDirectory(scenesDir);
                if (!result.success) {
                    this.scenes = [];
                    return;
                }

                const sceneFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const scenes = [];

                for (const file of sceneFiles) {
                    const filePath = `${scenesDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        // 解析Markdown文件的frontmatter
                        const content = fileResult.data;
                        const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);

                        if (frontmatterMatch) {
                            try {
                                // 使用简单的YAML解析
                                const yamlContent = frontmatterMatch[1];
                                const scene = this.parseSimpleYaml(yamlContent);
                                const description = frontmatterMatch[2].trim();

                                scene.filename = file.name;
                                scene.description = description;

                                // 检查是否有对应的草稿
                                const draftPath = `${basePath}/3_manuscript/drafts/${scene.scene_id}.md`;
                                const draftExists = await window.electronAPI.checkPathExists(draftPath);
                                scene.hasDraft = draftExists.exists;

                                scenes.push(scene);
                            } catch (parseError) {
                                console.error('解析场景文件失败:', file.name, parseError);
                            }
                        }
                    }
                }

                this.scenes = scenes;
            } catch (error) {
                console.error('加载场景数据失败:', error);
                this.scenes = [];
            }
        },

        // 简单的YAML解析器（仅用于场景卡片）
        parseSimpleYaml(yamlContent) {
            const result = {};
            const lines = yamlContent.split('\n');
            let currentKey = null;
            let currentArray = null;

            for (const line of lines) {
                const trimmed = line.trim();
                if (!trimmed || trimmed.startsWith('#')) continue;

                if (trimmed.startsWith('- ')) {
                    // 数组项
                    if (currentArray) {
                        const item = trimmed.substring(2).trim();
                        if (item.includes(':')) {
                            // 对象数组项
                            const [key, value] = item.split(':').map(s => s.trim());
                            const obj = {};
                            obj[key] = value;
                            currentArray.push(obj);
                        } else {
                            currentArray.push(item);
                        }
                    }
                } else if (trimmed.includes(':')) {
                    // 键值对
                    const colonIndex = trimmed.indexOf(':');
                    const key = trimmed.substring(0, colonIndex).trim();
                    const value = trimmed.substring(colonIndex + 1).trim();

                    if (value === '') {
                        // 可能是数组的开始
                        currentKey = key;
                        currentArray = [];
                        result[key] = currentArray;
                    } else {
                        result[key] = value;
                        currentKey = null;
                        currentArray = null;
                    }
                }
            }

            return result;
        },

        // 加载草稿数据
        async loadDrafts() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const draftsDir = `${basePath}/3_manuscript/drafts`;

                const result = await window.electronAPI.listDirectory(draftsDir);
                if (!result.success) {
                    this.drafts = [];
                    return;
                }

                const draftFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const drafts = [];

                for (const file of draftFiles) {
                    const filePath = `${draftsDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        const content = fileResult.data;
                        const title = file.name.replace('.md', '');
                        const preview = content.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';

                        // 获取文件修改时间（简化处理）
                        const lastModified = new Date().toLocaleDateString();

                        drafts.push({
                            id: title,
                            title,
                            filename: file.name,
                            content,
                            preview,
                            lastModified
                        });
                    }
                }

                this.drafts = drafts;
            } catch (error) {
                console.error('加载草稿数据失败:', error);
                this.drafts = [];
            }
        },

        // 加载已发布章节数据
        async loadPublishedChapters() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const publishedDir = `${basePath}/3_manuscript/published`;

                const result = await window.electronAPI.listDirectory(publishedDir);
                if (!result.success) {
                    this.publishedChapters = [];
                    return;
                }

                const publishedFiles = result.data.filter(f => f.isFile && f.name.endsWith('.md'));
                const chapters = [];

                for (const file of publishedFiles) {
                    const filePath = `${publishedDir}/${file.name}`;
                    const fileResult = await window.electronAPI.readFile(filePath);

                    if (fileResult.success) {
                        const content = fileResult.data;
                        const title = file.name.replace('.md', '');
                        const preview = content.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';

                        // 获取文件修改时间（简化处理）
                        const publishedAt = new Date().toLocaleDateString();

                        chapters.push({
                            id: title,
                            title,
                            filename: file.name,
                            content,
                            preview,
                            publishedAt
                        });
                    }
                }

                this.publishedChapters = chapters;
            } catch (error) {
                console.error('加载已发布章节失败:', error);
                this.publishedChapters = [];
            }
        },

        // 角色管理方法
        showCreateCharacterDialog() {
            this.editingCharacter = null;
            this.characterForm = {
                name: '',
                description: '',
                motivation: '',
                appearance: '',
                emotion: '',
                location: '',
                current_status: ''
            };
            this.showCharacterDialog = true;
        },

        editCharacter(character) {
            this.editingCharacter = character;
            this.characterForm = {
                name: character.name || '',
                description: character.description || '',
                motivation: character.motivation || '',
                appearance: character.appearance || '',
                emotion: character.state?.emotion || '',
                location: character.state?.location || '',
                current_status: character.state?.current_status || ''
            };
            this.showCharacterDialog = true;
        },

        closeCharacterDialog() {
            this.showCharacterDialog = false;
            this.editingCharacter = null;
            // 重置表单数据
            this.characterForm = {
                name: '',
                description: '',
                motivation: '',
                appearance: '',
                emotion: '',
                location: '',
                current_status: ''
            };
        },

        async saveCharacter() {
            if (!this.characterForm.name.trim()) {
                this.showNotification('请输入角色名称', 'warning');
                return;
            }

            // 验证角色名称是否已存在（编辑模式除外）
            if (!this.editingCharacter) {
                const existingCharacter = this.characters.find(c =>
                    c.name.toLowerCase() === this.characterForm.name.toLowerCase()
                );
                if (existingCharacter) {
                    this.showNotification('角色名称已存在', 'warning');
                    return;
                }
            }

            try {
                const basePath = this.currentProject.path;
                const characterFileName = this.editingCharacter ?
                    this.editingCharacter.filename :
                    `${this.generateSafeFileName(this.characterForm.name)}.yml`;

                const characterData = {
                    name: this.characterForm.name,
                    description: this.characterForm.description,
                    motivation: this.characterForm.motivation,
                    appearance: this.characterForm.appearance,
                    knowledge: [],
                    beliefs: [],
                    secrets: [],
                    state: {
                        emotion: this.characterForm.emotion || 'Neutral',
                        location: this.characterForm.location || '',
                        current_status: this.characterForm.current_status || '',
                        inventory: []
                    },
                    relationships: {}
                };

                // 用户编辑后删除AI生成标签
                // 注意：不在characterData中包含ai_generated字段，这样保存时就会自动删除

                const filePath = `${basePath}/1_knowledge_base/characters/${characterFileName}`;
                const result = await window.electronAPI.writeYaml(filePath, characterData);

                if (result.success) {
                    await this.loadCharacters();
                    await this.updateProjectStats();
                    this.closeCharacterDialog();
                } else {
                    alert('保存角色失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存角色失败:', error);
                alert('保存角色失败: ' + error.message);
            }
        },

        async deleteCharacter(character) {
            if (!confirm(`确定要删除角色 "${character.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/characters/${character.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadCharacters();
                    await this.updateProjectStats();
                } else {
                    alert('删除角色失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除角色失败:', error);
                alert('删除角色失败: ' + error.message);
            }
        },

        // 地点管理方法
        showCreateLocationDialog() {
            this.editingLocation = null;
            this.locationForm = {
                name: '',
                type: '',
                description: '',
                atmosphere: ''
            };
            this.showLocationDialog = true;
        },

        editLocation(location) {
            this.editingLocation = location;
            this.locationForm = {
                name: location.name || '',
                type: location.type || '',
                description: location.description || '',
                atmosphere: location.atmosphere || ''
            };
            this.showLocationDialog = true;
        },

        closeLocationDialog() {
            this.showLocationDialog = false;
            this.editingLocation = null;
            // 重置表单数据
            this.locationForm = {
                name: '',
                type: '',
                description: '',
                atmosphere: ''
            };
        },

        async saveLocation() {
            if (!this.locationForm.name.trim()) {
                alert('请输入地点名称');
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const locationId = this.editingLocation ?
                    this.editingLocation.id :
                    this.generateSafeFileName(this.locationForm.name);

                const locationData = {
                    name: this.locationForm.name,
                    type: this.locationForm.type,
                    description: this.locationForm.description,
                    atmosphere: this.locationForm.atmosphere,
                    features: [],
                    connections: []
                };

                // 用户编辑后删除AI生成标签

                const filePath = `${basePath}/1_knowledge_base/locations/${locationId}.yml`;
                const result = await window.electronAPI.writeYaml(filePath, locationData);

                if (result.success) {
                    await this.loadLocations();
                    await this.updateProjectStats();
                    this.closeLocationDialog();
                } else {
                    alert('保存地点失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存地点失败:', error);
                alert('保存地点失败: ' + error.message);
            }
        },

        async deleteLocation(location) {
            if (!confirm(`确定要删除地点 "${location.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/locations/${location.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadLocations();
                    await this.updateProjectStats();
                } else {
                    alert('删除地点失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除地点失败:', error);
                alert('删除地点失败: ' + error.message);
            }
        },

        // 组织管理方法
        showCreateOrganizationDialog() {
            this.editingOrganization = null;
            this.organizationForm = {
                name: '',
                type: '',
                description: '',
                leader: '',
                members: [],
                influence_level: '',
                status: '',
                headquarters: '',
                resources: ''
            };
            this.showOrganizationDialog = true;
        },

        editOrganization(organization) {
            this.editingOrganization = organization;
            this.organizationForm = {
                name: organization.name || '',
                type: organization.type || '',
                description: organization.description || '',
                leader: organization.leader || '',
                members: Array.isArray(organization.members) ? [...organization.members] : [],
                influence_level: organization.influence_level || '',
                status: organization.status || '',
                headquarters: organization.headquarters || '',
                resources: organization.resources || ''
            };
            this.showOrganizationDialog = true;
        },

        closeOrganizationDialog() {
            this.showOrganizationDialog = false;
            this.editingOrganization = null;
            // 重置表单数据
            this.organizationForm = {
                name: '',
                type: '',
                description: '',
                leader: '',
                members: [],
                influence_level: '',
                status: '',
                headquarters: '',
                resources: ''
            };
        },

        async saveOrganization() {
            if (!this.organizationForm.name.trim()) {
                this.showNotification('请输入组织名称', 'warning');
                return;
            }

            // 验证组织名称是否已存在（编辑模式除外）
            if (!this.editingOrganization) {
                const existingOrganization = this.organizations.find(o =>
                    o.name.toLowerCase() === this.organizationForm.name.toLowerCase()
                );
                if (existingOrganization) {
                    this.showNotification('组织名称已存在', 'warning');
                    return;
                }
            }

            try {
                const basePath = this.currentProject.path;
                const organizationId = this.editingOrganization ?
                    this.editingOrganization.id :
                    this.generateSafeFileName(this.organizationForm.name);

                const organizationData = {
                    name: this.organizationForm.name,
                    type: this.organizationForm.type,
                    description: this.organizationForm.description,
                    leader: this.organizationForm.leader,
                    members: this.organizationForm.members,
                    influence_level: this.organizationForm.influence_level,
                    status: this.organizationForm.status,
                    headquarters: this.organizationForm.headquarters,
                    resources: this.organizationForm.resources,
                    relationships: {},
                    goals: [],
                    history: []
                };

                // 用户编辑后删除AI生成标签

                const filePath = `${basePath}/1_knowledge_base/organizations/${organizationId}.yml`;
                const result = await window.electronAPI.writeYaml(filePath, organizationData);

                if (result.success) {
                    await this.loadOrganizations();
                    await this.updateProjectStats();
                    this.closeOrganizationDialog();
                    this.showNotification('组织保存成功', 'success');
                } else {
                    this.showNotification('保存组织失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存组织失败:', error);
                this.showNotification('保存组织失败: ' + error.message, 'error');
            }
        },

        async deleteOrganization(organization) {
            if (!confirm(`确定要删除组织 "${organization.name}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/1_knowledge_base/organizations/${organization.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadOrganizations();
                    await this.updateProjectStats();
                    this.showNotification('组织删除成功', 'success');
                } else {
                    this.showNotification('删除组织失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('删除组织失败:', error);
                this.showNotification('删除组织失败: ' + error.message, 'error');
            }
        },

        // ========== 工作流程管理相关方法 ==========

        // 显示工作流程配置对话框
        showWorkflowConfigDialog() {
            this.showWorkflowDialog = true;
        },

        // 关闭工作流程配置对话框
        closeWorkflowDialog() {
            this.showWorkflowDialog = false;
        },

        // 启动完整创作工作流程
        async startCompleteWorkflow() {
            if (!this.currentProject) {
                this.showNotification('请先打开一个项目', 'warning');
                return;
            }

            try {
                this.isGenerating = true;
                this.generatingText = '正在启动全自动创作流程...';
                this.generatingSubtext = '生成主线大纲';

                const result = await window.electronAPI.startCompleteWorkflow(
                    this.currentProject.path,
                    this.workflowForm
                );

                if (result.success) {
                    this.workflowStatus = result;
                    this.closeWorkflowDialog();
                    
                    if (result.phase === 'main_outline_review') {
                        this.showOutlineReviewDialog = true;
                        await this.loadOutlineForReview();
                    }
                    
                    this.showNotification('主线大纲已生成，请审阅编辑', 'success');
                } else {
                    this.showNotification('启动工作流程失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('启动工作流程失败:', error);
                this.showNotification('启动工作流程失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 获取工作流程状态
        async getWorkflowStatus() {
            if (!this.currentProject) return;

            try {
                const status = await window.electronAPI.getWorkflowStatus(this.currentProject.path);
                this.workflowStatus = status;
                return status;
            } catch (error) {
                console.error('获取工作流程状态失败:', error);
                return null;
            }
        },

        // 加载大纲以供审阅
        async loadOutlineForReview() {
            try {
                const outlinePath = `${this.currentProject.path}/2_plot/main_outline.md`;
                const result = await window.electronAPI.readFile(outlinePath);
                
                if (result.success) {
                    this.editedOutlineContent = result.data;
                } else {
                    this.showNotification('无法加载大纲文件', 'error');
                }
            } catch (error) {
                console.error('加载大纲失败:', error);
                this.showNotification('加载大纲失败: ' + error.message, 'error');
            }
        },

        // 确认主线大纲
        async confirmMainOutline() {
            try {
                this.isGenerating = true;
                this.generatingText = '确认主线大纲，生成分卷细纲...';
                this.generatingSubtext = '请耐心等待';

                const userEdits = this.outlineEditMode ? 
                    { content: this.editedOutlineContent } : null;

                const result = await window.electronAPI.confirmMainOutline(
                    this.currentProject.path,
                    userEdits
                );

                if (result.success) {
                    this.workflowStatus = result;
                    this.showOutlineReviewDialog = false;
                    
                    if (result.phase === 'volume_outlines_review') {
                        this.showVolumeReviewDialog = true;
                        await this.loadVolumeOutlinesForReview(result.data.volumes);
                    }
                    
                    this.showNotification('分卷细纲已生成，请审阅编辑', 'success');
                } else {
                    this.showNotification('确认大纲失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('确认主线大纲失败:', error);
                this.showNotification('确认主线大纲失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 加载分卷细纲以供审阅
        async loadVolumeOutlinesForReview(volumes) {
            try {
                this.editedVolumeOutlines = {};
                
                for (const volume of volumes) {
                    const outlinePath = `${this.currentProject.path}/2_plot/volumes/${volume.id}/outline.md`;
                    const result = await window.electronAPI.readFile(outlinePath);
                    
                    if (result.success) {
                        this.editedVolumeOutlines[volume.id] = {
                            id: volume.id,
                            title: volume.title,
                            content: result.data,
                            editMode: false
                        };
                    }
                }
            } catch (error) {
                console.error('加载分卷细纲失败:', error);
                this.showNotification('加载分卷细纲失败: ' + error.message, 'error');
            }
        },

        // 确认分卷细纲
        async confirmVolumeOutlines() {
            try {
                this.isGenerating = true;
                this.generatingText = '确认分卷细纲，准备开始创作...';
                this.generatingSubtext = '请耐心等待';

                const userEdits = {};
                Object.values(this.editedVolumeOutlines).forEach(volume => {
                    if (volume.editMode) {
                        userEdits[volume.id] = { content: volume.content };
                    }
                });

                const hasEdits = Object.keys(userEdits).length > 0;
                const result = await window.electronAPI.confirmVolumeOutlines(
                    this.currentProject.path,
                    hasEdits ? userEdits : null
                );

                if (result.success) {
                    this.workflowStatus = result;
                    this.showVolumeReviewDialog = false;
                    this.showNotification('分卷细纲确认完成，可以开始分卷创作', 'success');
                } else {
                    this.showNotification('确认分卷细纲失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('确认分卷细纲失败:', error);
                this.showNotification('确认分卷细纲失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 开始分卷创作
        async startVolumeWriting(volumeId) {
            this.selectedVolume = volumeId;
            this.showVolumeWritingDialog = true;
        },

        // 确认开始分卷创作
        async confirmVolumeWriting() {
            try {
                this.isGenerating = true;
                this.generatingText = `正在创作第${this.selectedVolume}卷...`;
                this.generatingSubtext = '这可能需要较长时间，请耐心等待';

                const writingConfig = {
                    chapters_per_batch: this.workflowForm.chapters_per_batch,
                    words_per_chapter: this.workflowForm.average_chapter_words,
                    pause_between_chapters: false
                };

                const result = await window.electronAPI.startVolumeWriting(
                    this.currentProject.path,
                    this.selectedVolume,
                    writingConfig
                );

                if (result.success) {
                    this.showVolumeWritingDialog = false;
                    this.showNotification(
                        `第${this.selectedVolume}卷创作完成，共创建${result.chapters_completed}章`,
                        'success',
                        10000
                    );
                    
                    // 刷新项目状态
                    await this.getWorkflowStatus();
                    await this.updateProjectStats();
                    await this.loadDrafts();
                    await this.loadPublishedChapters();
                } else {
                    this.showNotification('分卷创作失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('分卷创作失败:', error);
                this.showNotification('分卷创作失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 切换大纲编辑模式
        toggleOutlineEditMode() {
            this.outlineEditMode = !this.outlineEditMode;
        },

        // 切换分卷细纲编辑模式
        toggleVolumeEditMode(volumeId) {
            if (this.editedVolumeOutlines[volumeId]) {
                this.editedVolumeOutlines[volumeId].editMode = 
                    !this.editedVolumeOutlines[volumeId].editMode;
            }
        },

        // 关闭各种对话框
        closeOutlineReviewDialog() {
            this.showOutlineReviewDialog = false;
        },

        closeVolumeReviewDialog() {
            this.showVolumeReviewDialog = false;
        },

        closeVolumeWritingDialog() {
            this.showVolumeWritingDialog = false;
        },

        // 计算预估章节数
        getEstimatedChapters() {
            if (this.workflowForm.target_total_words && this.workflowForm.average_chapter_words) {
                return Math.ceil(this.workflowForm.target_total_words / this.workflowForm.average_chapter_words);
            }
            return 0;
        },

        // 计算分卷章节分布
        getChaptersPerVolume() {
            const totalChapters = this.getEstimatedChapters();
            if (totalChapters && this.workflowForm.target_volumes) {
                return Math.ceil(totalChapters / this.workflowForm.target_volumes);
            }
            return 0;
        },

        // 获取工作流程阶段显示文本
        getWorkflowPhaseText(phase) {
            const phases = {
                'initial': '准备阶段',
                'main_outline': '生成主线大纲',
                'main_outline_review': '审阅主线大纲',
                'volume_outlines': '生成分卷细纲',
                'volume_outlines_review': '审阅分卷细纲',
                'volume_writing_ready': '准备开始创作',
                'volume_writing': '分卷创作中',
                'completed': '创作完成'
            };
            return phases[phase] || '未知阶段';
        },

        // 检查是否可以开始某个分卷的创作
        canStartVolumeWriting(volumeId) {
            if (!this.workflowStatus || !this.workflowStatus.volumes) return false;
            
            const volume = this.workflowStatus.volumes[volumeId];
            return volume && !volume.writing_completed && 
                   this.workflowStatus.current_phase === 'volume_writing_ready';
        },

        // 场景管理方法
        showCreateSceneDialog() {
            this.editingScene = null;
            this.sceneForm = {
                scene_id: '',
                storyline: 'main',
                characters: [],
                location: '',
                goals: '',
                description: ''
            };
            this.showSceneDialog = true;
        },

        editScene(scene) {
            this.editingScene = scene;
            this.sceneForm = {
                scene_id: scene.scene_id || '',
                storyline: scene.storyline || 'main',
                characters: Array.isArray(scene.characters) ? [...scene.characters] : [],
                location: scene.location || '',
                goals: scene.goal || '',
                description: scene.description || ''
            };
            this.showSceneDialog = true;
        },

        closeSceneDialog() {
            this.showSceneDialog = false;
            this.editingScene = null;
            // 重置表单数据
            this.sceneForm = {
                scene_id: '',
                storyline: 'main',
                characters: [],
                location: '',
                goals: '',
                description: ''
            };
        },

        // 显示AI创建场景对话框
        showAiCreateSceneDialog() {
            this.aiSceneForm = {
                scene_id: '',
                characters: [],
                excludeCharacters: [],
                location: '',
                goals: '',
                description: ''
            };
            this.showAiSceneDialog = true;
        },

        closeAiSceneDialog() {
            this.showAiSceneDialog = false;
            // 重置表单数据
            this.aiSceneForm = {
                scene_id: '',
                characters: [],
                excludeCharacters: [],
                location: '',
                goals: '',
                description: ''
            };
        },

        // AI创建场景（带参数）
        async aiCreateSceneWithParams() {
            if (!this.currentProject) {
                this.showNotification('请先打开一个项目', 'warning');
                return;
            }

            if (!this.apiConfigured) {
                this.showNotification('请先配置API设置', 'warning');
                return;
            }

            // 验证参与角色和排除角色不能有重复
            const participatingChars = this.aiSceneForm.characters || [];
            const excludingChars = this.aiSceneForm.excludeCharacters || [];
            const conflictingChars = participatingChars.filter(char => excludingChars.includes(char));
            
            if (conflictingChars.length > 0) {
                this.showNotification(`角色 "${conflictingChars.join(', ')}" 不能同时被选为参与角色和排除角色`, 'warning');
                return;
            }

            this.isGenerating = true;
            this.generatingText = 'AI正在创建场景...';
            this.generatingSubtext = '正在分析项目信息并生成场景卡片';

            try {
                // 构建用户指定的参数 - 创建纯净的对象避免Vue响应式代理问题
                const userParams = {
                    scene_id: this.aiSceneForm.scene_id ? this.aiSceneForm.scene_id.trim() : null,
                    characters: this.aiSceneForm.characters && this.aiSceneForm.characters.length > 0 ?
                        [...this.aiSceneForm.characters] : null,
                    excludeCharacters: this.aiSceneForm.excludeCharacters && this.aiSceneForm.excludeCharacters.length > 0 ?
                        [...this.aiSceneForm.excludeCharacters] : null,
                    location: this.aiSceneForm.location ? this.aiSceneForm.location.trim() : null,
                    goals: this.aiSceneForm.goals ? this.aiSceneForm.goals.trim() : null,
                    description: this.aiSceneForm.description ? this.aiSceneForm.description.trim() : null
                };

                // 创建一个完全纯净的对象，避免任何Vue响应式代理
                const cleanUserParams = JSON.parse(JSON.stringify(userParams));
                console.log('发送AI创建场景请求，参数:', cleanUserParams);

                const result = await window.electronAPI.aiCreateScene(this.currentProject.path, cleanUserParams);

                console.log('AI创建场景响应:', result);

                if (result.success) {
                    const sceneData = result.sceneData;

                    // 填充场景表单
                    this.sceneForm = {
                        scene_id: sceneData.scene_id || '',
                        storyline: sceneData.storyline || 'main',
                        characters: sceneData.characters || [],
                        location: sceneData.location || '',
                        goals: sceneData.goals || '',
                        description: sceneData.description || ''
                    };

                    // 处理建议的新卡片
                    if (sceneData.suggested_new_characters && sceneData.suggested_new_characters.length > 0) {
                        await this.createSuggestedCards('characters', sceneData.suggested_new_characters);
                    }

                    if (sceneData.suggested_new_locations && sceneData.suggested_new_locations.length > 0) {
                        await this.createSuggestedCards('locations', sceneData.suggested_new_locations);
                    }

                    if (sceneData.suggested_new_organizations && sceneData.suggested_new_organizations.length > 0) {
                        await this.createSuggestedCards('organizations', sceneData.suggested_new_organizations);
                    }

                    this.showNotification('AI场景创建成功！请检查并完善场景信息', 'success');
                    this.closeAiSceneDialog();
                } else {
                    this.showNotification('AI创建失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('AI创建场景失败:', error);
                this.showNotification('AI创建失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 创建建议的卡片
        async createSuggestedCards(type, cards) {
            const basePath = this.currentProject.path;

            for (const card of cards) {
                try {
                    // 添加AI生成标签
                    card.ai_generated = true;

                    let filePath, yamlContent;

                    if (type === 'characters') {
                        const characterId = card.name.toLowerCase().replace(/\s+/g, '_');
                        filePath = `${basePath}/1_knowledge_base/characters/${characterId}.yml`;
                        yamlContent = jsyaml.dump(card, { indent: 2 });
                    } else if (type === 'locations') {
                        const locationId = card.name.toLowerCase().replace(/\s+/g, '_');
                        filePath = `${basePath}/1_knowledge_base/locations/${locationId}.yml`;
                        yamlContent = jsyaml.dump(card, { indent: 2 });
                    } else if (type === 'organizations') {
                        const orgId = card.name.toLowerCase().replace(/\s+/g, '_');
                        filePath = `${basePath}/1_knowledge_base/organizations/${orgId}.yml`;
                        yamlContent = jsyaml.dump(card, { indent: 2 });
                    }

                    if (filePath && yamlContent) {
                        await window.electronAPI.writeFile(filePath, yamlContent);
                    }
                } catch (error) {
                    console.error(`创建${type}卡片失败:`, error);
                }
            }

            // 重新加载相关数据
            if (type === 'characters') {
                await this.loadCharacters();
            } else if (type === 'locations') {
                await this.loadLocations();
            } else if (type === 'organizations') {
                await this.loadOrganizations();
            }
        },



        async saveScene() {
            if (!this.sceneForm.scene_id.trim()) {
                alert('请输入场景ID');
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const newSceneId = this.sceneForm.scene_id.trim();

                // 构建场景数据
                const sceneData = {
                    scene_id: newSceneId,
                    storyline: this.sceneForm.storyline,
                    characters: this.sceneForm.characters,
                    location: this.sceneForm.location,
                    goal: this.sceneForm.goals
                };

                // 构建Markdown内容
                let yamlContent = '';
                yamlContent += `scene_id: ${sceneData.scene_id}\n`;
                yamlContent += `storyline: ${sceneData.storyline}\n`;

                if (sceneData.characters.length > 0) {
                    yamlContent += 'characters:\n';
                    sceneData.characters.forEach(char => {
                        yamlContent += `  - ${char}\n`;
                    });
                }

                if (sceneData.location) {
                    yamlContent += `location: ${sceneData.location}\n`;
                }

                if (sceneData.goal && sceneData.goal.trim()) {
                    yamlContent += `goal: ${sceneData.goal}\n`;
                }

                const markdownContent = `---\n${yamlContent}---\n\n${this.sceneForm.description}`;

                // 处理编辑模式
                if (this.editingScene) {
                    const oldFileName = this.editingScene.filename;
                    const newFilePath = `${basePath}/2_plot/scene_cards/${newSceneId}.md`;
                    const oldFilePath = `${basePath}/2_plot/scene_cards/${oldFileName}`;

                    // 如果场景ID改变了，需要删除旧文件
                    if (oldFileName !== `${newSceneId}.md`) {
                        // 先写入新文件
                        const writeResult = await window.electronAPI.writeFile(newFilePath, markdownContent);
                        if (writeResult.success) {
                            // 删除旧文件
                            await window.electronAPI.deleteFile(oldFilePath);
                        } else {
                            alert('保存场景失败: ' + writeResult.error);
                            return;
                        }
                    } else {
                        // 场景ID没有改变，直接更新文件
                        const result = await window.electronAPI.writeFile(newFilePath, markdownContent);
                        if (!result.success) {
                            alert('保存场景失败: ' + result.error);
                            return;
                        }
                    }
                } else {
                    // 新建模式
                    const filePath = `${basePath}/2_plot/scene_cards/${newSceneId}.md`;
                    const result = await window.electronAPI.writeFile(filePath, markdownContent);
                    if (!result.success) {
                        alert('保存场景失败: ' + result.error);
                        return;
                    }
                }

                await this.loadScenes();
                await this.updateProjectStats();
                this.closeSceneDialog();
                this.showNotification('场景保存成功', 'success');

            } catch (error) {
                console.error('保存场景失败:', error);
                alert('保存场景失败: ' + error.message);
            }
        },

        async deleteScene(scene) {
            if (!confirm(`确定要删除场景 "${scene.scene_id}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/2_plot/scene_cards/${scene.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadScenes();
                    await this.updateProjectStats();
                } else {
                    alert('删除场景失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除场景失败:', error);
                alert('删除场景失败: ' + error.message);
            }
        },

        async generateDraft(scene) {
            if (scene.hasDraft) {
                this.showNotification('该场景已有草稿', 'warning');
                return;
            }

            if (!this.apiConfigured) {
                this.showNotification('请先配置AI API设置', 'warning');
                this.showApiConfigDialog();
                return;
            }

            try {
                this.isGenerating = true;
                this.generatingText = 'AI正在生成初稿...';
                this.generatingSubtext = '正在分析场景信息和上下文，这可能需要几十秒时间';

                // 创建一个纯净的场景数据对象，避免Vue响应式代理
                const cleanSceneData = {
                    scene_id: scene.scene_id,
                    storyline: scene.storyline,
                    characters: Array.isArray(scene.characters) ? [...scene.characters] : [],
                    location: scene.location,
                    goal: scene.goal || '',
                    description: scene.description
                };

                const result = await window.electronAPI.generateSceneDraft(
                    this.currentProject.path,
                    cleanSceneData
                );

                if (result.success) {
                    // 重新加载场景数据以更新hasDraft状态
                    await this.loadScenes();
                    await this.loadDrafts();
                    await this.updateProjectStats();
                    this.showNotification('初稿生成成功！请在草稿箱中查看。', 'success');
                } else {
                    this.showNotification('生成失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('生成初稿失败:', error);
                this.showNotification('生成失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 伏笔管理方法
        async loadForeshadowingData() {
            if (!this.currentProject) return;

            try {
                const result = await window.electronAPI.loadForeshadowingData(this.currentProject.path);

                if (result.success) {
                    const data = result.data;
                    this.activeForeshadowing = data.active_foreshadowing || [];
                    this.resolvedForeshadowing = data.resolved_foreshadowing || [];
                    this.plannedForeshadowing = data.planned_foreshadowing || [];
                } else {
                    console.error('加载伏笔数据失败:', result.error);
                    this.activeForeshadowing = [];
                    this.resolvedForeshadowing = [];
                    this.plannedForeshadowing = [];
                }
            } catch (error) {
                console.error('加载伏笔数据失败:', error);
                this.activeForeshadowing = [];
                this.resolvedForeshadowing = [];
                this.plannedForeshadowing = [];
            }
        },

        async refreshForeshadowingData() {
            await this.loadForeshadowingData();
            this.showNotification('伏笔数据已刷新', 'success');
        },

        showAddForeshadowingDialog() {
            this.editingForeshadowing = null;
            this.foreshadowingForm = {
                type: 'character_secret',
                description: '',
                hints: [],
                importance: 5,
                estimated_payoff_distance: 3,
                tags: [],
                relevant_characters: [],
                relevant_locations: [],
                planted_in_scene: '',
                implementation_notes: ''
            };
            this.showForeshadowingDialog = true;
        },

        editForeshadowing(foreshadowing) {
            this.editingForeshadowing = foreshadowing;
            this.foreshadowingForm = {
                type: foreshadowing.type || 'character_secret',
                description: foreshadowing.description || '',
                hints: [...(foreshadowing.hints || [])],
                importance: foreshadowing.importance || 5,
                estimated_payoff_distance: foreshadowing.estimated_payoff_distance || 3,
                tags: [...(foreshadowing.tags || [])],
                relevant_characters: [...(foreshadowing.relevant_characters || [])],
                relevant_locations: [...(foreshadowing.relevant_locations || [])],
                planted_in_scene: foreshadowing.planted_in_scene || '',
                implementation_notes: foreshadowing.implementation_notes || ''
            };
            this.showForeshadowingDialog = true;
        },

        closeForeshadowingDialog() {
            this.showForeshadowingDialog = false;
            this.editingForeshadowing = null;
        },

        async saveForeshadowing() {
            if (!this.foreshadowingForm.description.trim()) {
                alert('请输入伏笔描述');
                return;
            }

            try {
                const foreshadowingData = {
                    type: this.foreshadowingForm.type,
                    description: this.foreshadowingForm.description,
                    hints: this.foreshadowingForm.hints.filter(h => h.trim()),
                    importance: this.foreshadowingForm.importance,
                    estimated_payoff_distance: this.foreshadowingForm.estimated_payoff_distance,
                    tags: this.foreshadowingForm.tags.filter(t => t.trim()),
                    relevant_characters: this.foreshadowingForm.relevant_characters.filter(c => c.trim()),
                    relevant_locations: this.foreshadowingForm.relevant_locations.filter(l => l.trim()),
                    planted_in_scene: this.foreshadowingForm.planted_in_scene,
                    implementation_notes: this.foreshadowingForm.implementation_notes
                };

                let result;
                if (this.editingForeshadowing) {
                    // 更新现有伏笔
                    result = await window.electronAPI.updateForeshadowing(
                        this.currentProject.path,
                        this.editingForeshadowing.id,
                        foreshadowingData
                    );
                } else {
                    // 添加新伏笔
                    result = await window.electronAPI.addForeshadowing(
                        this.currentProject.path,
                        [foreshadowingData]
                    );
                }

                if (result.success) {
                    await this.loadForeshadowingData();
                    this.closeForeshadowingDialog();
                    this.showNotification(this.editingForeshadowing ? '伏笔更新成功' : '伏笔添加成功', 'success');
                } else {
                    alert('保存伏笔失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存伏笔失败:', error);
                alert('保存伏笔失败: ' + error.message);
            }
        },

        async deleteForeshadowing(foreshadowing) {
            if (!confirm(`确定要删除伏笔 "${foreshadowing.description}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const result = await window.electronAPI.deleteForeshadowing(
                    this.currentProject.path,
                    foreshadowing.id
                );

                if (result.success) {
                    await this.loadForeshadowingData();
                    this.showNotification('伏笔删除成功', 'success');
                } else {
                    alert('删除伏笔失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除伏笔失败:', error);
                alert('删除伏笔失败: ' + error.message);
            }
        },

        markForeshadowingResolved(foreshadowing) {
            this.resolvingForeshadowing = foreshadowing;
            this.foreshadowingResolveForm = {
                resolved_in_scene: '',
                resolution_details: ''
            };
            this.showForeshadowingResolveDialog = true;
        },

        closeForeshadowingResolveDialog() {
            this.showForeshadowingResolveDialog = false;
            this.resolvingForeshadowing = null;
        },

        async saveForeshadowingResolution() {
            if (!this.foreshadowingResolveForm.resolved_in_scene.trim()) {
                alert('请输入回收场景');
                return;
            }

            try {
                const result = await window.electronAPI.markForeshadowingResolved(
                    this.currentProject.path,
                    this.resolvingForeshadowing.id,
                    this.foreshadowingResolveForm.resolved_in_scene,
                    this.foreshadowingResolveForm.resolution_details
                );

                if (result.success) {
                    await this.loadForeshadowingData();
                    this.closeForeshadowingResolveDialog();
                    this.showNotification('伏笔标记为已回收', 'success');
                } else {
                    alert('标记伏笔回收失败: ' + result.error);
                }
            } catch (error) {
                console.error('标记伏笔回收失败:', error);
                alert('标记伏笔回收失败: ' + error.message);
            }
        },

        async activatePlannedForeshadowing(foreshadowing) {
            try {
                const result = await window.electronAPI.activatePlannedForeshadowing(
                    this.currentProject.path,
                    foreshadowing.id
                );

                if (result.success) {
                    await this.loadForeshadowingData();
                    this.showNotification('伏笔已激活', 'success');
                } else {
                    alert('激活伏笔失败: ' + result.error);
                }
            } catch (error) {
                console.error('激活伏笔失败:', error);
                alert('激活伏笔失败: ' + error.message);
            }
        },

        getForeshadowingTypeText(type) {
            const types = {
                'character_secret': '角色秘密',
                'item_clue': '物品线索',
                'event_foreshadowing': '事件预示',
                'ability_hint': '能力暗示',
                'relationship_clue': '关系线索',
                'world_expansion': '世界观扩展',
                'plot_twist_setup': '情节转折预示'
            };
            return types[type] || type;
        },

        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        },

        // API配置相关方法
        checkApiConfiguration() {
            if (this.currentProject && this.currentProject.config) {
                const config = this.currentProject.config;

                // 向后兼容：如果存在旧的api_settings，迁移到新格式
                if (config.api_settings && !config.analysis_api && !config.generation_api) {
                    config.analysis_api = {
                        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
                        api_key: config.api_settings.api_key || '',
                        model: config.api_settings.model || 'gpt-3.5-turbo',
                        temperature: 0.3 // 分析用较低的temperature
                    };
                    config.generation_api = {
                        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
                        api_key: config.api_settings.api_key || '',
                        model: config.api_settings.model || 'gpt-3.5-turbo',
                        temperature: config.api_settings.temperature || 0.7
                    };
                    // 删除旧的配置
                    delete config.api_settings;

                    // 保存迁移后的配置
                    this.saveConfigToFile();
                }

                // 加载新的ai_apis配置
                if (config.ai_apis) {
                    Object.keys(this.aiApisForm).forEach(apiType => {
                        if (config.ai_apis[apiType]) {
                            this.aiApisForm[apiType] = {
                                enabled: config.ai_apis[apiType].enabled !== false,
                                base_url: config.ai_apis[apiType].base_url || 'https://api.openai.com/v1',
                                api_key: config.ai_apis[apiType].api_key || '',
                                model: config.ai_apis[apiType].model || this.apiTypes[apiType].defaultModel,
                                temperature: config.ai_apis[apiType].temperature || this.aiApisForm[apiType].temperature,
                                description: config.ai_apis[apiType].description || this.aiApisForm[apiType].description
                            };
                        }
                    });
                } else {
                    // 如果没有ai_apis配置，从旧格式迁移
                    const analysisApi = config.analysis_api;
                    const generationApi = config.generation_api;

                    if (analysisApi) {
                        this.aiApisForm.analysis = {
                            enabled: true,
                            base_url: analysisApi.base_url || 'https://api.openai.com/v1',
                            api_key: analysisApi.api_key || '',
                            model: analysisApi.model || 'gpt-3.5-turbo',
                            temperature: analysisApi.temperature || 0.3,
                            description: this.aiApisForm.analysis.description
                        };
                    }

                    if (generationApi) {
                        this.aiApisForm.primary_generation = {
                            enabled: true,
                            base_url: generationApi.base_url || 'https://api.openai.com/v1',
                            api_key: generationApi.api_key || '',
                            model: generationApi.model || 'gpt-3.5-turbo',
                            temperature: generationApi.temperature || 0.7,
                            description: this.aiApisForm.primary_generation.description
                        };
                    }
                }

                // 向后兼容：同时更新旧的表单数据
                const analysisApi = config.analysis_api;
                const generationApi = config.generation_api;

                if (analysisApi) {
                    this.analysisApiForm = {
                        base_url: analysisApi.base_url || 'https://api.openai.com/v1',
                        api_key: analysisApi.api_key || '',
                        model: analysisApi.model || 'gpt-3.5-turbo',
                        temperature: analysisApi.temperature || 0.3
                    };
                }

                if (generationApi) {
                    this.generationApiForm = {
                        base_url: generationApi.base_url || 'https://api.openai.com/v1',
                        api_key: generationApi.api_key || '',
                        model: generationApi.model || 'gpt-3.5-turbo',
                        temperature: generationApi.temperature || 0.7
                    };
                }

                // 更新API配置状态
                this.updateApiConfiguredStatus();
            }
        },

        // 更新API配置状态
        updateApiConfiguredStatus() {
            if (this.currentProject && this.currentProject.config) {
                const config = this.currentProject.config;

                // 检查新的ai_apis配置
                if (config.ai_apis) {
                    this.apiConfigured = Object.values(config.ai_apis).some(api =>
                        api.enabled && api.api_key && api.api_key.trim()
                    );
                } else {
                    // 向后兼容：检查旧的API配置
                    const analysisApi = config.analysis_api;
                    const generationApi = config.generation_api;
                    this.apiConfigured = !!(
                        (analysisApi && analysisApi.api_key) ||
                        (generationApi && generationApi.api_key)
                    );
                }
            }
        },

        // 保存配置到文件
        async saveConfigToFile() {
            if (this.currentProject) {
                try {
                    const configPath = `${this.currentProject.path}/chronicler.yml`;
                    // 创建一个可序列化的配置副本
                    const configCopy = JSON.parse(JSON.stringify(this.currentProject.config));
                    await window.electronAPI.writeYaml(configPath, configCopy);
                } catch (error) {
                    console.error('保存配置文件失败:', error);
                }
            }
        },

        showApiConfigDialog() {
            this.showApiDialog = true;
        },

        closeApiDialog() {
            this.showApiDialog = false;
        },

        // 设置分析API模型
        setAnalysisModel(model) {
            this.analysisApiForm.model = model;
        },

        // 设置生成API模型
        setGenerationModel(model) {
            this.generationApiForm.model = model;
        },

        // 设置指定API类型的模型
        setApiModel(apiType, model) {
            if (this.aiApisForm[apiType]) {
                this.aiApisForm[apiType].model = model;
            }
        },

        // 获取API状态
        getApiStatus(apiType) {
            const api = this.aiApisForm[apiType];
            if (!api.enabled) return 'disabled';
            if (!api.api_key || !api.api_key.trim()) return 'error';
            return 'success';
        },

        // 获取API状态文本
        getApiStatusText(apiType) {
            const status = this.getApiStatus(apiType);
            switch (status) {
                case 'disabled': return '已禁用';
                case 'error': return '未配置';
                case 'success': return '已配置';
                default: return '未知';
            }
        },

        async saveApiConfig() {
            // 检查至少有一个启用的API配置了密钥
            const hasValidApi = Object.values(this.aiApisForm).some(api =>
                api.enabled && api.api_key && api.api_key.trim()
            );

            if (!hasValidApi) {
                this.showNotification('请至少启用并配置一个API的密钥', 'warning');
                return;
            }

            try {
                // 更新项目配置
                const config = { ...this.currentProject.config };

                // 更新新的ai_apis配置
                config.ai_apis = {};
                Object.keys(this.aiApisForm).forEach(apiType => {
                    const apiForm = this.aiApisForm[apiType];
                    config.ai_apis[apiType] = {
                        enabled: apiForm.enabled,
                        base_url: apiForm.base_url.trim() || 'https://api.openai.com/v1',
                        api_key: apiForm.api_key.trim(),
                        model: apiForm.model || this.apiTypes[apiType].defaultModel,
                        temperature: parseFloat(apiForm.temperature),
                        description: apiForm.description
                    };
                });

                // 向后兼容：同时更新旧的API配置格式
                if (this.aiApisForm.analysis.enabled) {
                    config.analysis_api = {
                        base_url: this.aiApisForm.analysis.base_url.trim() || 'https://api.openai.com/v1',
                        api_key: this.aiApisForm.analysis.api_key.trim(),
                        model: this.aiApisForm.analysis.model || 'gpt-3.5-turbo',
                        temperature: parseFloat(this.aiApisForm.analysis.temperature)
                    };
                }

                if (this.aiApisForm.primary_generation.enabled) {
                    config.generation_api = {
                        base_url: this.aiApisForm.primary_generation.base_url.trim() || 'https://api.openai.com/v1',
                        api_key: this.aiApisForm.primary_generation.api_key.trim(),
                        model: this.aiApisForm.primary_generation.model || 'gpt-3.5-turbo',
                        temperature: parseFloat(this.aiApisForm.primary_generation.temperature)
                    };
                }

                const configPath = `${this.currentProject.path}/chronicler.yml`;
                const result = await window.electronAPI.writeYaml(configPath, config);

                if (result.success) {
                    this.currentProject.config = config;
                    this.updateApiConfiguredStatus();
                    this.closeApiDialog();
                    this.showNotification('API配置保存成功！', 'success');
                } else {
                    this.showNotification('保存配置失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存API配置失败:', error);
                this.showNotification('保存配置失败: ' + error.message, 'error');
            }
        },

        // 设置模型
        setModel(modelName) {
            this.apiForm.model = modelName;
        },

        // 生成安全的文件名
        generateSafeFileName(name) {
            if (!name || !name.trim()) {
                return 'unnamed_' + Date.now();
            }

            // 移除特殊字符，保留中文、英文、数字
            let safeName = name.trim()
                .replace(/[<>:"/\\|?*]/g, '') // 移除Windows不允许的字符
                .replace(/\s+/g, '_') // 空格替换为下划线
                .replace(/[^\u4e00-\u9fa5a-zA-Z0-9_-]/g, '') // 只保留中文、英文、数字、下划线、连字符
                .substring(0, 50); // 限制长度

            // 如果处理后为空，使用时间戳
            if (!safeName) {
                safeName = 'item_' + Date.now();
            }

            return safeName;
        },

        // 世界观设定相关方法
        async loadWorldBible() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const worldBiblePath = `${basePath}/1_knowledge_base/world_bible.md`;

                const result = await window.electronAPI.readFile(worldBiblePath);
                if (result.success) {
                    this.worldBibleContent = result.data;
                    this.worldBibleChanged = false;
                } else {
                    this.worldBibleContent = '';
                    this.worldBibleChanged = false;
                }
            } catch (error) {
                console.error('加载世界观设定失败:', error);
                this.worldBibleContent = '';
                this.worldBibleChanged = false;
            }
        },

        onWorldBibleInput() {
            this.worldBibleChanged = true;

            // 自动保存（5秒后）
            if (this.worldBibleAutoSaveTimer) {
                clearTimeout(this.worldBibleAutoSaveTimer);
            }

            this.worldBibleAutoSaveTimer = setTimeout(() => {
                this.saveWorldBible();
            }, 5000);
        },

        async saveWorldBible() {
            if (!this.currentProject || !this.worldBibleChanged) return;

            try {
                const basePath = this.currentProject.path;
                const worldBiblePath = `${basePath}/1_knowledge_base/world_bible.md`;

                const result = await window.electronAPI.writeFile(worldBiblePath, this.worldBibleContent);

                if (result.success) {
                    this.worldBibleChanged = false;
                    this.showNotification('世界观设定已保存', 'success', 2000);
                } else {
                    this.showNotification('保存失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存世界观设定失败:', error);
                this.showNotification('保存失败: ' + error.message, 'error');
            }
        },

        insertWorldSection(sectionName) {
            const editor = this.$refs.worldBibleEditor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (sectionName) {
                case '基本设定':
                    insertText = `\n\n## 基本设定\n\n### 时代背景\n- 时间：\n- 地点：\n- 社会制度：\n\n### 物理法则\n- 魔法系统：\n- 科技水平：\n- 特殊规则：\n\n`;
                    break;
                case '历史背景':
                    insertText = `\n\n## 历史背景\n\n### 重要事件\n- \n\n### 传说故事\n- \n\n### 历史人物\n- \n\n`;
                    break;
                case '文化设定':
                    insertText = `\n\n## 文化设定\n\n### 语言文字\n- \n\n### 宗教信仰\n- \n\n### 社会习俗\n- \n\n`;
                    break;
                case '地理环境':
                    insertText = `\n\n## 地理环境\n\n### 主要地区\n- \n\n### 气候特征\n- \n\n### 自然资源\n- \n\n`;
                    break;
                case '魔法系统':
                    insertText = `\n\n## 魔法系统\n\n### 魔法原理\n- \n\n### 魔法分类\n- \n\n### 使用限制\n- \n\n`;
                    break;
            }

            this.worldBibleContent = this.worldBibleContent.substring(0, cursorPos) + insertText + this.worldBibleContent.substring(cursorPos);
            this.onWorldBibleInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        },

        // 主线大纲相关方法
        async loadOutline() {
            if (!this.currentProject) return;

            try {
                const basePath = this.currentProject.path;
                const outlinePath = `${basePath}/2_plot/main_outline.md`;

                const result = await window.electronAPI.readFile(outlinePath);
                if (result.success) {
                    this.outlineContent = result.data;
                    this.outlineChanged = false;
                } else {
                    this.outlineContent = '';
                    this.outlineChanged = false;
                }
            } catch (error) {
                console.error('加载主线大纲失败:', error);
                this.outlineContent = '';
                this.outlineChanged = false;
            }
        },

        onOutlineInput() {
            this.outlineChanged = true;

            // 自动保存（5秒后）
            if (this.outlineAutoSaveTimer) {
                clearTimeout(this.outlineAutoSaveTimer);
            }

            this.outlineAutoSaveTimer = setTimeout(() => {
                this.saveOutline();
            }, 5000);
        },

        async saveOutline() {
            if (!this.currentProject || !this.outlineChanged) return;

            try {
                const basePath = this.currentProject.path;
                const outlinePath = `${basePath}/2_plot/main_outline.md`;

                const result = await window.electronAPI.writeFile(outlinePath, this.outlineContent);

                if (result.success) {
                    this.outlineChanged = false;
                    this.showNotification('主线大纲已保存', 'success', 2000);
                } else {
                    this.showNotification('保存失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('保存主线大纲失败:', error);
                this.showNotification('保存失败: ' + error.message, 'error');
            }
        },

        insertOutlineSection(sectionName) {
            const editor = this.$refs.outlineEditor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (sectionName) {
                case '故事概述':
                    insertText = `\n\n## 故事概述\n\n### 核心冲突\n- \n\n### 主要情节线\n- \n\n### 故事主题\n- \n\n`;
                    break;
                case '主要角色':
                    insertText = `\n\n## 主要角色\n\n### 主角\n- 姓名：\n- 性格：\n- 目标：\n\n### 配角\n- \n\n### 反派\n- \n\n`;
                    break;
                case '情节结构':
                    insertText = `\n\n## 情节结构\n\n### 第一幕：开端\n- \n\n### 第二幕：发展\n- \n\n### 第三幕：高潮\n- \n\n### 第四幕：结局\n- \n\n`;
                    break;
                case '主题思想':
                    insertText = `\n\n## 主题思想\n\n### 核心主题\n- \n\n### 价值观念\n- \n\n### 寓意表达\n- \n\n`;
                    break;
                case '分章计划':
                    insertText = `\n\n## 分章计划\n\n### 第一章\n- 标题：\n- 内容：\n- 目标：\n\n### 第二章\n- 标题：\n- 内容：\n- 目标：\n\n`;
                    break;
            }

            this.outlineContent = this.outlineContent.substring(0, cursorPos) + insertText + this.outlineContent.substring(cursorPos);
            this.onOutlineInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        },

        // 文本编辑器相关方法
        editDraft(draft) {
            this.editingDraft = draft;
            this.editorContent = draft.content;
            this.isSaved = true;
            this.showEditorDialog = true;

            // 聚焦到编辑器
            this.$nextTick(() => {
                if (this.$refs.editor) {
                    this.$refs.editor.focus();
                }
            });
        },

        closeEditorDialog() {
            if (!this.isSaved) {
                if (!confirm('有未保存的更改，确定要关闭吗？')) {
                    return;
                }
            }

            this.showEditorDialog = false;
            this.editingDraft = null;
            this.editorContent = '';
            this.isSaved = true;

            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }
        },

        onEditorInput() {
            this.isSaved = false;

            // 自动保存（3秒后）
            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
            }

            this.autoSaveTimer = setTimeout(() => {
                this.saveDraft();
            }, 3000);
        },

        onEditorKeydown(event) {
            // Ctrl+S 保存
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                this.saveDraft();
            }
        },

        async saveDraft() {
            if (!this.editingDraft) return;

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/3_manuscript/drafts/${this.editingDraft.filename}`;

                const result = await window.electronAPI.writeFile(filePath, this.editorContent);

                if (result.success) {
                    this.isSaved = true;
                    // 更新草稿内容
                    this.editingDraft.content = this.editorContent;
                    this.editingDraft.preview = this.editorContent.substring(0, 200).replace(/[#>\n]/g, ' ').trim() + '...';
                } else {
                    alert('保存失败: ' + result.error);
                }
            } catch (error) {
                console.error('保存草稿失败:', error);
                alert('保存失败: ' + error.message);
            }
        },

        async publishDraft() {
            if (!this.editingDraft || !this.editorContent.trim()) {
                this.showNotification('内容不能为空', 'warning');
                return;
            }

            if (!this.apiConfigured) {
                this.showNotification('请先配置AI API设置以进行状态分析', 'warning');
                this.showApiConfigDialog();
                return;
            }

            if (!confirm('确定要提交这个草稿吗？提交后将进行AI分析并自动更新角色状态。')) {
                return;
            }

            try {
                this.isGenerating = true;
                this.generatingText = 'AI正在分析场景...';
                this.generatingSubtext = '正在提取状态变化并更新角色信息';

                // 先保存当前内容
                await this.saveDraft();

                // 提取场景ID（从文件名中）
                const sceneId = this.editingDraft.filename.replace('.md', '');

                // 1. AI分析并生成摘要，自动更新角色状态
                const analysisResult = await window.electronAPI.analyzeAndUpdateState(
                    this.currentProject.path,
                    sceneId,
                    this.editorContent
                );

                if (!analysisResult.success) {
                    this.showNotification('AI分析失败: ' + analysisResult.error, 'error');
                    return;
                }

                // 2. 移动文件到已发布目录
                const basePath = this.currentProject.path;
                const draftPath = `${basePath}/3_manuscript/drafts/${this.editingDraft.filename}`;
                const publishedPath = `${basePath}/3_manuscript/published/${this.editingDraft.filename}`;

                // 读取草稿内容并写入到已发布目录
                const result = await window.electronAPI.writeFile(publishedPath, this.editorContent);

                if (result.success) {
                    // 3. 删除草稿文件
                    await window.electronAPI.deleteFile(draftPath);

                    // 4. 重新加载所有数据以反映状态变化
                    await this.loadDrafts();
                    await this.loadPublishedChapters();
                    await this.loadCharacters(); // 重新加载角色以显示状态更新
                    await this.loadScenes(); // 重新加载场景
                    await this.updateProjectStats();

                    this.closeEditorDialog();

                    // 5. 显示分析结果
                    this.showAnalysisResult(analysisResult.summary);

                } else {
                    this.showNotification('提交失败: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('提交草稿失败:', error);
                this.showNotification('提交失败: ' + error.message, 'error');
            } finally {
                this.isGenerating = false;
            }
        },

        // 显示AI分析结果
        showAnalysisResult(summary) {
            // 首先显示成功通知
            this.showNotification('章节提交成功！AI分析已完成，角色状态已更新。', 'success', 3000);

            // 然后显示详细的分析结果
            setTimeout(() => {
                let message = '📊 AI分析结果：\n\n';

                // 显示角色状态更新
                if (summary.state_updates && Object.keys(summary.state_updates).length > 0) {
                    message += '角色状态更新：\n';
                    Object.entries(summary.state_updates).forEach(([charId, updates]) => {
                        message += `• ${charId}:\n`;
                        if (updates.emotion) message += `  - 情绪: ${updates.emotion}\n`;
                        if (updates.location) message += `  - 位置: ${updates.location}\n`;
                        if (updates.inventory_add && updates.inventory_add.length > 0) {
                            message += `  - 新增物品: ${updates.inventory_add.join(', ')}\n`;
                        }
                    });
                    message += '\n';
                }

                // 显示关系更新
                if (summary.relationship_updates && Object.keys(summary.relationship_updates).length > 0) {
                    message += '关系变化：\n';
                    Object.entries(summary.relationship_updates).forEach(([charId, relationships]) => {
                        Object.entries(relationships).forEach(([otherChar, rel]) => {
                            message += `• ${charId} ↔ ${otherChar}: ${rel.status}\n`;
                        });
                    });
                    message += '\n';
                }

                // 显示新信息
                if (summary.new_information_revealed && summary.new_information_revealed.length > 0) {
                    message += '新揭露信息：\n';
                    summary.new_information_revealed.forEach(info => {
                        message += `• ${info}\n`;
                    });
                    message += '\n';
                }

                // 显示下一场景建议
                if (summary.next_scene_recommendation) {
                    message += `下一场景建议：\n${summary.next_scene_recommendation}`;
                }

                // 如果有实际的更新内容，才显示详细信息
                if (message.length > 20) {
                    alert(message);
                }
            }, 3500);
        },

        async deleteDraft(draft) {
            if (!confirm(`确定要删除草稿 "${draft.title}" 吗？此操作不可撤销。`)) {
                return;
            }

            try {
                const basePath = this.currentProject.path;
                const filePath = `${basePath}/3_manuscript/drafts/${draft.filename}`;

                const result = await window.electronAPI.deleteFile(filePath);

                if (result.success) {
                    await this.loadDrafts();
                    await this.updateProjectStats();
                } else {
                    alert('删除失败: ' + result.error);
                }
            } catch (error) {
                console.error('删除草稿失败:', error);
                alert('删除失败: ' + error.message);
            }
        },

        viewChapter(chapter) {
            // 简单的查看功能，在新窗口中显示内容
            const newWindow = window.open('', '_blank');
            newWindow.document.write(`
                <html>
                <head>
                    <title>${chapter.title}</title>
                    <style>
                        body { font-family: Georgia, serif; line-height: 1.8; max-width: 800px; margin: 0 auto; padding: 20px; }
                        h1 { color: #2c3e50; }
                    </style>
                </head>
                <body>
                    <h1>${chapter.title}</h1>
                    <pre style="white-space: pre-wrap; font-family: Georgia, serif;">${chapter.content}</pre>
                </body>
                </html>
            `);
        },

        // 编辑器工具栏功能
        formatText(format) {
            const editor = this.$refs.editor;
            if (!editor) return;

            const start = editor.selectionStart;
            const end = editor.selectionEnd;
            const selectedText = this.editorContent.substring(start, end);

            if (!selectedText) {
                alert('请先选择要格式化的文本');
                return;
            }

            let formattedText = selectedText;
            switch (format) {
                case 'bold':
                    formattedText = `**${selectedText}**`;
                    break;
                case 'italic':
                    formattedText = `*${selectedText}*`;
                    break;
                case 'underline':
                    formattedText = `<u>${selectedText}</u>`;
                    break;
            }

            this.editorContent = this.editorContent.substring(0, start) + formattedText + this.editorContent.substring(end);
            this.onEditorInput();

            // 重新设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(start, start + formattedText.length);
            });
        },

        insertText(type) {
            const editor = this.$refs.editor;
            if (!editor) return;

            const cursorPos = editor.selectionStart;
            let insertText = '';

            switch (type) {
                case 'dialogue':
                    insertText = '\n\n"对话内容，"角色说道。\n\n';
                    break;
                case 'description':
                    insertText = '\n\n[在这里添加环境或动作描述]\n\n';
                    break;
                case 'action':
                    insertText = '\n\n角色进行了某个动作。\n\n';
                    break;
            }

            this.editorContent = this.editorContent.substring(0, cursorPos) + insertText + this.editorContent.substring(cursorPos);
            this.onEditorInput();

            // 设置光标位置
            this.$nextTick(() => {
                editor.focus();
                editor.setSelectionRange(cursorPos + insertText.length, cursorPos + insertText.length);
            });
        }
    },
    
    mounted() {
        // 监听菜单事件
        window.electronAPI.onMenuNewProject(() => {
            this.createNewProject();
        });

        window.electronAPI.onMenuOpenProject(() => {
            this.openExistingProject();
        });

        // 监听窗口关闭事件，清理定时器
        window.addEventListener('beforeunload', () => {
            if (this.autoSaveTimer) {
                clearTimeout(this.autoSaveTimer);
            }
            if (this.notification.timer) {
                clearTimeout(this.notification.timer);
            }
            if (this.worldBibleAutoSaveTimer) {
                clearTimeout(this.worldBibleAutoSaveTimer);
            }
            if (this.outlineAutoSaveTimer) {
                clearTimeout(this.outlineAutoSaveTimer);
            }
        });

        // 监听键盘快捷键
        window.addEventListener('keydown', (event) => {
            // Ctrl+N 新建项目
            if (event.ctrlKey && event.key === 'n') {
                event.preventDefault();
                this.createNewProject();
            }
            // Ctrl+O 打开项目
            if (event.ctrlKey && event.key === 'o') {
                event.preventDefault();
                this.openExistingProject();
            }
            // Esc 关闭对话框
            if (event.key === 'Escape') {
                if (this.showCharacterDialog) this.closeCharacterDialog();
                if (this.showLocationDialog) this.closeLocationDialog();
                if (this.showOrganizationDialog) this.closeOrganizationDialog();
                if (this.showSceneDialog) this.closeSceneDialog();
                if (this.showApiDialog) this.closeApiDialog();
                if (this.showEditorDialog) this.closeEditorDialog();
                if (this.showForeshadowingDialog) this.closeForeshadowingDialog();
                if (this.showForeshadowingResolveDialog) this.closeForeshadowingResolveDialog();
                if (this.notification.show) this.hideNotification();
            }
        });
    },
    
    beforeUnmount() {
        // 清理事件监听器
        window.electronAPI.removeAllListeners('menu-new-project');
        window.electronAPI.removeAllListeners('menu-open-project');
    }
}).mount('#app');

directories:
  output: dist
  buildResources: build
appId: com.chronicler.app
productName: Chronicler
files:
  - filter:
      - '**/*'
      - '!dist'
      - '!.git'
      - '!README.md'
nodeGypRebuild: false
buildDependenciesFromSource: false
win:
  target: nsis
  icon: assets/icon.ico
mac:
  target: dmg
  icon: assets/icon.icns
linux:
  target: AppImage
  icon: assets/icon.png
electronVersion: 28.3.3

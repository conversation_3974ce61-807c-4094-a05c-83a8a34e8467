const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// 暴露受保护的方法给渲染进程，在window.electronAPI下
contextBridge.exposeInMainWorld('electronAPI', {
  // 文件系统操作
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data) => ipcRenderer.invoke('write-file', filePath, data),
  readYaml: (filePath) => ipcRenderer.invoke('read-yaml', filePath),
  writeYaml: (filePath, data) => ipcRenderer.invoke('write-yaml', filePath, data),
  createDirectory: (dirPath) => ipcRenderer.invoke('create-directory', dirPath),
  checkPathExists: (path) => ipcRenderer.invoke('check-path-exists', path),
  listDirectory: (dirPath) => ipcRenderer.invoke('list-directory', dirPath),
  deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),

  // AI生成功能
  generateSceneDraft: (projectPath, sceneData) => ipcRenderer.invoke('generate-scene-draft', projectPath, sceneData),
  analyzeAndUpdateState: (projectPath, sceneId, content) => ipcRenderer.invoke('analyze-and-update-state', projectPath, sceneId, content),
  aiCreateScene: (projectPath, userParams) => ipcRenderer.invoke('ai-create-scene', projectPath, userParams),

  // 大纲生成功能
  generateMainOutline: (projectPath, requirements) => ipcRenderer.invoke('generate-main-outline', projectPath, requirements),
  generateVolumeOutline: (projectPath, volumeId, guidance) => ipcRenderer.invoke('generate-volume-outline', projectPath, volumeId, guidance),

  // 伏笔管理功能
  initializeForeshadowing: (projectPath) => ipcRenderer.invoke('initialize-foreshadowing', projectPath),
  analyzeSceneForeshadowing: (projectPath, sceneId, content) => ipcRenderer.invoke('analyze-scene-foreshadowing', projectPath, sceneId, content),
  getPayoffOpportunities: (projectPath, sceneData) => ipcRenderer.invoke('get-payoff-opportunities', projectPath, sceneData),
  loadForeshadowingData: (projectPath) => ipcRenderer.invoke('load-foreshadowing-data', projectPath),
  addForeshadowing: (projectPath, foreshadowingList) => ipcRenderer.invoke('add-foreshadowing', projectPath, foreshadowingList),
  updateForeshadowing: (projectPath, foreshadowingId, updateData) => ipcRenderer.invoke('update-foreshadowing', projectPath, foreshadowingId, updateData),
  deleteForeshadowing: (projectPath, foreshadowingId) => ipcRenderer.invoke('delete-foreshadowing', projectPath, foreshadowingId),
  markForeshadowingResolved: (projectPath, foreshadowingId, sceneId, resolutionDetails) => ipcRenderer.invoke('mark-foreshadowing-resolved', projectPath, foreshadowingId, sceneId, resolutionDetails),
  activatePlannedForeshadowing: (projectPath, foreshadowingId) => ipcRenderer.invoke('activate-planned-foreshadowing', projectPath, foreshadowingId),

  // 全自动创作功能
  startAutoWriting: (projectPath, config) => ipcRenderer.invoke('start-auto-writing', projectPath, config),
  initializeAutoWriter: (projectPath) => ipcRenderer.invoke('initialize-auto-writer', projectPath),

  // 工作流程管理功能
  startCompleteWorkflow: (projectPath, config) => ipcRenderer.invoke('start-complete-workflow', projectPath, config),
  getWorkflowStatus: (projectPath) => ipcRenderer.invoke('get-workflow-status', projectPath),
  confirmMainOutline: (projectPath, userEdits) => ipcRenderer.invoke('confirm-main-outline', projectPath, userEdits),
  confirmVolumeOutlines: (projectPath, userEdits) => ipcRenderer.invoke('confirm-volume-outlines', projectPath, userEdits),
  startVolumeWriting: (projectPath, volumeId, writingConfig) => ipcRenderer.invoke('start-volume-writing', projectPath, volumeId, writingConfig),
  pauseWorkflow: (projectPath, reason) => ipcRenderer.invoke('pause-workflow', projectPath, reason),
  resumeWorkflow: (projectPath) => ipcRenderer.invoke('resume-workflow', projectPath),
  initializeWorkflow: (projectPath) => ipcRenderer.invoke('initialize-workflow', projectPath),

  // 菜单事件监听
  onMenuNewProject: (callback) => ipcRenderer.on('menu-new-project', callback),
  onMenuOpenProject: (callback) => ipcRenderer.on('menu-open-project', callback),

  // 移除监听器
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

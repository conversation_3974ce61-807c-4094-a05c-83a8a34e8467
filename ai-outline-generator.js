const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');
const { getApiConfig, initializeOpenAI } = require('./api-config-utils');

/**
 * AI大纲生成器 - 支持总大纲、分卷管理和细纲生成
 */
class AIOutlineGenerator {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.volumesPath = path.join(projectPath, '2_plot', 'volumes');
    this.outlinePath = path.join(projectPath, '2_plot', 'main_outline.md');
  }

  /**
   * 生成主线大纲
   */
  async generateMainOutline(userRequirements = {}) {
    try {
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config);
      
      // 读取现有的世界观设定
      const worldBible = await this.loadWorldBible();
      const existingCharacters = await this.loadAllCharacters();
      
      const prompt = this.buildMainOutlinePrompt(worldBible, existingCharacters, userRequirements, config.novel_planning);
      
      const apiConfig = this.getApiConfig(config, 'planning');
      const completion = await openai.chat.completions.create({
        model: apiConfig.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说大纲策划师。请根据世界观设定和角色信息，生成一个完整的、具有吸引力的网文大纲。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: config.generation_api.temperature || 0.7
      });

      const outlineContent = completion.choices[0].message.content;
      
      // 保存主线大纲
      await fs.writeFile(this.outlinePath, outlineContent, 'utf8');
      
      // 解析并生成分卷结构
      const volumes = await this.parseVolumesFromOutline(outlineContent);
      await this.createVolumeStructure(volumes);
      
      return {
        success: true,
        outlineContent,
        volumes: volumes.length,
        outlinePath: this.outlinePath
      };
      
    } catch (error) {
      console.error('生成主线大纲失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成分卷细纲
   */
  async generateVolumeOutline(volumeId, userGuidance = {}) {
    try {
      const config = await this.loadConfig();
      const openai = this.initializeOpenAI(config);
      
      // 加载上下文信息
      const context = await this.compileVolumeContext(volumeId);
      const prompt = this.buildVolumeOutlinePrompt(volumeId, context, userGuidance);
      
      const apiConfig = this.getApiConfig(config, 'planning');
      const completion = await openai.chat.completions.create({
        model: apiConfig.model || 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的网络小说分卷策划师。请根据主线大纲和当前分卷要求，生成详细的分卷细纲，包含具体的章节规划和情节安排。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: apiConfig.temperature || 0.7
      });

      const volumeOutlineContent = completion.choices[0].message.content;
      
      // 保存分卷细纲
      const volumeOutlinePath = path.join(this.volumesPath, `${volumeId}`, 'outline.md');
      await this.ensureDirectoryExists(path.dirname(volumeOutlinePath));
      await fs.writeFile(volumeOutlinePath, volumeOutlineContent, 'utf8');
      
      // 解析章节信息并创建场景卡片模板
      const chapters = await this.parseChaptersFromVolumeOutline(volumeOutlineContent, volumeId);
      await this.createChapterTemplates(volumeId, chapters);
      
      return {
        success: true,
        volumeOutlineContent,
        chapters: chapters.length,
        volumeOutlinePath
      };
      
    } catch (error) {
      console.error('生成分卷细纲失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前分卷的细纲（用于生成时的上下文）
   */
  async getCurrentVolumeOutline(sceneId) {
    try {
      // 从场景ID确定所属分卷
      const volumeId = this.extractVolumeFromSceneId(sceneId);
      if (!volumeId) return null;
      
      const volumeOutlinePath = path.join(this.volumesPath, volumeId, 'outline.md');
      try {
        const content = await fs.readFile(volumeOutlinePath, 'utf8');
        return {
          volumeId,
          content,
          path: volumeOutlinePath
        };
      } catch (e) {
        return null;
      }
    } catch (error) {
      console.error('获取当前分卷细纲失败:', error);
      return null;
    }
  }

  /**
   * 构建主线大纲生成的Prompt
   */
  buildMainOutlinePrompt(worldBible, characters, userRequirements, novelPlanning = null) {
    let prompt = `请基于以下信息生成一个完整的网络小说主线大纲：

## 世界观设定
${worldBible || '暂无世界观设定'}

## 现有角色
`;
    
    if (characters.length > 0) {
      characters.forEach(char => {
        prompt += `### ${char.name}\n`;
        prompt += `- 描述: ${char.description || '未设定'}\n`;
        prompt += `- 动机: ${char.motivation || '未设定'}\n`;
        prompt += `- 能力: ${char.abilities || '未设定'}\n\n`;
      });
    } else {
      prompt += '暂无角色信息\n\n';
    }

    // 添加小说规划信息
    if (novelPlanning) {
      prompt += `## 小说规划约束
- 目标总字数: ${novelPlanning.target_total_words?.toLocaleString() || '未设定'}字
- 平均章节字数: ${novelPlanning.average_chapter_words || '未设定'}字
- 预估总章节数: ${novelPlanning.estimated_total_chapters || '未计算'}章
- 目标分卷数: ${novelPlanning.volumes?.target_volume_count || '未设定'}卷
- 每卷预估章节数: ${novelPlanning.volumes?.chapters_per_volume || '未计算'}章
- 每卷预估字数: ${novelPlanning.volumes?.words_per_volume?.toLocaleString() || '未计算'}字

`;
    }

    prompt += `## 用户要求
`;
    if (userRequirements.genre) {
      prompt += `- 小说类型: ${userRequirements.genre}\n`;
    }
    if (userRequirements.length) {
      prompt += `- 预期长度: ${userRequirements.length}\n`;
    }
    if (userRequirements.theme) {
      prompt += `- 主题要求: ${userRequirements.theme}\n`;
    }
    if (userRequirements.style) {
      prompt += `- 风格偏好: ${userRequirements.style}\n`;
    }
    if (userRequirements.special_requirements) {
      prompt += `- 特殊要求: ${userRequirements.special_requirements}\n`;
    }

    prompt += `
## 大纲生成要求

请生成一个完整的主线大纲，包含以下结构：

### 1. 故事概述
- 核心冲突
- 主要故事线
- 预期字数和章节数

### 2. 分卷规划
请严格按照上述小说规划约束进行分卷：`
    
    if (novelPlanning && novelPlanning.volumes) {
      prompt += `
- 总计 ${novelPlanning.volumes.target_volume_count || 4} 个分卷
- 每卷约 ${novelPlanning.volumes.chapters_per_volume || 25} 章
- 每卷约 ${novelPlanning.volumes.words_per_volume?.toLocaleString() || '50万'} 字`;
    } else {
      prompt += `
- 分为3-5个分卷，每个分卷80-125章`;
    }
    
    prompt += `

每个分卷应该：
- 有明确的主题和目标
- 有独立的故事弧
- 与整体故事形成有机联系

格式：
**第X卷：分卷名称**
- 主要目标：描述该卷的核心目标
- 关键事件：列出3-5个关键情节点
- 角色发展：主要角色在该卷的成长
- 章节范围：第X章-第Y章

### 3. 主要角色弧
- 每个重要角色的发展轨迹
- 角色关系的变化
- 重要的角色转折点

### 4. 核心伏笔规划
- 主要伏笔的埋设和回收时机
- 重要秘密的揭示节奏
- 关键道具/能力的出现时机

### 5. 高潮设计
- 每卷的小高潮
- 全书的大高潮
- 冲突的逐步升级

请确保大纲符合网络小说的节奏，有足够的爽点和冲突，张弛有度，避免平铺直叙也避免爽点过于密集。`;

    return prompt;
  }

  /**
   * 构建分卷细纲生成的Prompt
   */
  buildVolumeOutlinePrompt(volumeId, context, userGuidance) {
    let prompt = `请为第${volumeId}卷生成详细的分卷细纲：

## 主线大纲参考
${context.mainOutline}

## 本卷基本信息
${context.volumeInfo}

## 小说规划约束
${context.novelPlanning ? `
- 本卷目标章节数: ${context.novelPlanning.volumes?.chapters_per_volume || '约25'}章
- 本卷目标字数: ${context.novelPlanning.volumes?.words_per_volume?.toLocaleString() || '约50万'}字
- 平均章节字数: ${context.novelPlanning.average_chapter_words || 2000}字
- 质量标准: ${context.novelPlanning.quality_standards?.min_chapter_words || 1500}-${context.novelPlanning.quality_standards?.max_chapter_words || 3000}字/章
` : '无具体规划约束'}

## 已发布章节情况
`;
    if (context.publishedChapters.length > 0) {
      prompt += `已完成章节：\n`;
      context.publishedChapters.forEach(chapter => {
        prompt += `- ${chapter.title}\n`;
      });
    } else {
      prompt += '暂无已发布章节\n';
    }

    prompt += `
## 当前角色状态
`;
    if (context.characterStates.length > 0) {
      context.characterStates.forEach(char => {
        prompt += `### ${char.name}\n`;
        prompt += `- 当前位置: ${char.state?.location || '未知'}\n`;
        prompt += `- 情绪状态: ${char.state?.emotion || '未知'}\n`;
        prompt += `- 当前状态: ${char.state?.current_status || '未设定'}\n\n`;
      });
    }

    if (userGuidance.focus_characters) {
      prompt += `\n## 本卷重点角色\n${userGuidance.focus_characters.join(', ')}\n`;
    }
    if (userGuidance.key_events) {
      prompt += `\n## 本卷关键事件\n${userGuidance.key_events}\n`;
    }
    if (userGuidance.target_chapters) {
      prompt += `\n## 目标章节数\n${userGuidance.target_chapters}章\n`;
    }

    prompt += `
## 细纲生成要求

请生成本卷的详细细纲，包含：

### 1. 分卷主题与目标
- 本卷的核心主题
- 要达成的故事目标
- 角色成长目标

### 2. 详细章节规划
对每一章进行规划，格式如下：

**第X章：章节标题**
- 主要场景：发生地点
- 参与角色：[角色1, 角色2, ...]
- 核心事件：本章要发生的关键事件
- 角色发展：角色在本章的变化
- 伏笔处理：本章要埋设或回收的伏笔
- 冲突设计：本章的主要冲突点
- 爽点设计：读者期待的爽点内容
- 章节目标：本章要达成的具体目标
- 字数预估：预计字数

### 3. 分卷内的伏笔规划
- 本卷新增伏笔
- 本卷需要回收的伏笔
- 为后续分卷埋设的长线伏笔

### 4. 角色关系发展
- 主要角色关系在本卷的变化
- 新角色的引入时机
- 重要角色的退场安排

### 5. 节奏控制
- 紧张章节与舒缓章节的安排
- 高潮章节的分布
- 过渡章节的处理

请确保紧张章节都有明确的目标和冲突，舒缓章节也要有趣，符合网文的快节奏要求。`;

    return prompt;
  }

  /**
   * 从大纲中解析分卷信息
   */
  async parseVolumesFromOutline(outlineContent) {
    const volumes = [];
    const volumeRegex = /\*\*第(\d+)卷[：:](.*?)\*\*/g;
    let match;

    while ((match = volumeRegex.exec(outlineContent)) !== null) {
      const volumeNumber = match[1];
      const volumeTitle = match[2].trim();
      
      volumes.push({
        id: `vol_${volumeNumber}`,
        number: parseInt(volumeNumber),
        title: volumeTitle,
        status: 'planned'
      });
    }

    return volumes;
  }

  /**
   * 创建分卷目录结构
   */
  async createVolumeStructure(volumes) {
    await this.ensureDirectoryExists(this.volumesPath);
    
    for (const volume of volumes) {
      const volumeDir = path.join(this.volumesPath, volume.id);
      await this.ensureDirectoryExists(volumeDir);
      
      // 创建分卷配置文件
      const volumeConfig = {
        id: volume.id,
        number: volume.number,
        title: volume.title,
        status: volume.status,
        created_at: new Date().toISOString(),
        chapters: []
      };
      
      const configPath = path.join(volumeDir, 'config.yml');
      await fs.writeFile(configPath, yaml.dump(volumeConfig, { indent: 2 }), 'utf8');
    }
  }

  /**
   * 从分卷细纲中解析章节信息
   */
  async parseChaptersFromVolumeOutline(volumeOutlineContent, volumeId) {
    const chapters = [];
    const chapterRegex = /\*\*第(\d+)章[：:](.*?)\*\*/g;
    let match;

    while ((match = chapterRegex.exec(volumeOutlineContent)) !== null) {
      const chapterNumber = match[1];
      const chapterTitle = match[2].trim();
      
      chapters.push({
        id: `第${chapterNumber}章_${chapterTitle.replace(/[^\w\u4e00-\u9fa5]/g, '_')}`,
        number: parseInt(chapterNumber),
        title: chapterTitle,
        volume_id: volumeId,
        status: 'planned'
      });
    }

    return chapters;
  }

  /**
   * 创建章节模板
   */
  async createChapterTemplates(volumeId, chapters) {
    const sceneCardsDir = path.join(this.projectPath, '2_plot', 'scene_cards');
    await this.ensureDirectoryExists(sceneCardsDir);
    
    for (const chapter of chapters) {
      const sceneCardPath = path.join(sceneCardsDir, `${chapter.id}.md`);
      
      // 检查是否已存在
      try {
        await fs.access(sceneCardPath);
        continue; // 如果已存在则跳过
      } catch (e) {
        // 文件不存在，创建模板
      }
      
      const template = `---
scene_id: ${chapter.id}
storyline: main
volume_id: ${volumeId}
chapter_number: ${chapter.number}
characters: []
location: ""
goal: ""
description: ""
status: planned
ai_generated: true
---

# ${chapter.title}

## 场景目标
待规划

## 场景描述
待规划

## 参与角色
待规划

## 关键事件
待规划
`;

      await fs.writeFile(sceneCardPath, template, 'utf8');
    }
  }

  /**
   * 编译分卷上下文信息
   */
  async compileVolumeContext(volumeId) {
    const context = {
      mainOutline: '',
      volumeInfo: '',
      novelPlanning: null,
      publishedChapters: [],
      characterStates: []
    };

    try {
      // 读取项目配置和小说规划
      try {
        const config = await this.loadConfig();
        context.novelPlanning = config.novel_planning;
      } catch (e) {
        console.warn('无法加载项目配置:', e.message);
      }

      // 读取主线大纲
      try {
        context.mainOutline = await fs.readFile(this.outlinePath, 'utf8');
      } catch (e) {
        context.mainOutline = '暂无主线大纲';
      }

      // 读取分卷信息
      const volumeConfigPath = path.join(this.volumesPath, volumeId, 'config.yml');
      try {
        const volumeConfigContent = await fs.readFile(volumeConfigPath, 'utf8');
        const volumeConfig = yaml.load(volumeConfigContent);
        context.volumeInfo = `第${volumeConfig.number}卷：${volumeConfig.title}`;
      } catch (e) {
        context.volumeInfo = `分卷ID: ${volumeId}`;
      }

      // 读取已发布章节
      const publishedDir = path.join(this.projectPath, '3_manuscript', 'published');
      try {
        const publishedFiles = await fs.readdir(publishedDir);
        for (const file of publishedFiles.filter(f => f.endsWith('.md'))) {
          context.publishedChapters.push({
            title: file.replace('.md', '')
          });
        }
      } catch (e) {
        // 目录不存在时忽略
      }

      // 读取角色当前状态
      const charactersDir = path.join(this.projectPath, '1_knowledge_base', 'characters');
      try {
        const characterFiles = await fs.readdir(charactersDir);
        for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
          const charPath = path.join(charactersDir, file);
          const charContent = await fs.readFile(charPath, 'utf8');
          const charData = yaml.load(charContent);
          context.characterStates.push(charData);
        }
      } catch (e) {
        // 目录不存在时忽略
      }

    } catch (error) {
      console.error('编译分卷上下文失败:', error);
    }

    return context;
  }

  /**
   * 从场景ID提取分卷ID
   */
  extractVolumeFromSceneId(sceneId) {
    // 假设场景ID格式为：第X章_xxx
    // 需要根据章节号映射到分卷
    const chapterMatch = sceneId.match(/第(\d+)章/);
    if (!chapterMatch) return null;
    
    const chapterNumber = parseInt(chapterMatch[1]);
    
    // 简单的分卷映射逻辑（可以根据实际需求调整）
    if (chapterNumber <= 25) return 'vol_1';
    if (chapterNumber <= 50) return 'vol_2';
    if (chapterNumber <= 75) return 'vol_3';
    if (chapterNumber <= 100) return 'vol_4';
    return 'vol_5';
  }

  /**
   * 加载项目配置
   */
  async loadConfig() {
    const configPath = path.join(this.projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    // 向后兼容
    if (config.api_settings && !config.generation_api) {
      config.generation_api = {
        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
        api_key: config.api_settings.api_key || '',
        model: config.api_settings.model || 'gpt-3.5-turbo',
        temperature: config.api_settings.temperature || 0.7
      };
    }

    // 计算并更新小说规划数据
    if (config.novel_planning) {
      config.novel_planning = this.calculateNovelPlanning(config.novel_planning);
    }

    return config;
  }

  /**
   * 计算小说规划数据
   */
  calculateNovelPlanning(planning) {
    const calculated = { ...planning };
    
    // 计算预估总章节数
    if (calculated.target_total_words && calculated.average_chapter_words) {
      calculated.estimated_total_chapters = Math.ceil(
        calculated.target_total_words / calculated.average_chapter_words
      );
    }
    
    // 计算分卷数据
    if (calculated.volumes && calculated.estimated_total_chapters) {
      const volumeCount = calculated.volumes.target_volume_count || 4;
      calculated.volumes.chapters_per_volume = Math.ceil(
        calculated.estimated_total_chapters / volumeCount
      );
      calculated.volumes.words_per_volume = Math.ceil(
        calculated.target_total_words / volumeCount
      );
    }
    
    return calculated;
  }

  /**
   * 初始化OpenAI客户端
   */
  initializeOpenAI(config, apiType = 'planning') {
    return initializeOpenAI(config, apiType);
  }

  /**
   * 获取API配置
   */
  getApiConfig(config, apiType = 'planning') {
    return getApiConfig(config, apiType);
  }

  /**
   * 加载世界观设定
   */
  async loadWorldBible() {
    try {
      const worldBiblePath = path.join(this.projectPath, '1_knowledge_base', 'world_bible.md');
      return await fs.readFile(worldBiblePath, 'utf8');
    } catch (e) {
      return null;
    }
  }

  /**
   * 加载所有角色
   */
  async loadAllCharacters() {
    const characters = [];
    try {
      const charactersDir = path.join(this.projectPath, '1_knowledge_base', 'characters');
      const characterFiles = await fs.readdir(charactersDir);
      
      for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
        const charPath = path.join(charactersDir, file);
        const charContent = await fs.readFile(charPath, 'utf8');
        const charData = yaml.load(charContent);
        characters.push(charData);
      }
    } catch (e) {
      // 目录不存在时忽略
    }
    
    return characters;
  }

  /**
   * 确保目录存在
   */
  async ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }
}

/**
 * 导出函数接口（保持向后兼容）
 */
async function generateMainOutline(projectPath, userRequirements = {}) {
  const generator = new AIOutlineGenerator(projectPath);
  return await generator.generateMainOutline(userRequirements);
}

async function generateVolumeOutline(projectPath, volumeId, userGuidance = {}) {
  const generator = new AIOutlineGenerator(projectPath);
  return await generator.generateVolumeOutline(volumeId, userGuidance);
}

async function getCurrentVolumeOutline(projectPath, sceneId) {
  const generator = new AIOutlineGenerator(projectPath);
  return await generator.getCurrentVolumeOutline(sceneId);
}

module.exports = {
  AIOutlineGenerator,
  generateMainOutline,
  generateVolumeOutline,
  getCurrentVolumeOutline
}; 
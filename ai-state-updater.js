const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');
const { getApiConfig, initializeOpenAI } = require('./api-config-utils');

// 分析场景并更新角色状态的核心函数
async function analyzeSceneAndUpdateState(projectPath, sceneId, content) {
  try {
    // 1. 读取项目配置
    const configPath = path.join(projectPath, 'chronicler.yml');
    const configContent = await fs.readFile(configPath, 'utf8');
    const config = yaml.load(configContent);

    // 向后兼容：处理旧的api_settings配置
    if (config.api_settings && !config.analysis_api) {
      config.analysis_api = {
        base_url: config.api_settings.base_url || 'https://api.openai.com/v1',
        api_key: config.api_settings.api_key || '',
        model: config.api_settings.model || 'gpt-3.5-turbo',
        temperature: 0.3 // 分析用较低的temperature
      };
    }

    // 2. 初始化OpenAI客户端（使用统一的API配置工具）
    const apiType = 'analysis';
    const apiConfig = getApiConfig(config, apiType);
    const openai = initializeOpenAI(config, apiType);

    // 3. 读取场景卡片信息
    const sceneCardPath = path.join(projectPath, '2_plot', 'scene_cards', `${sceneId}.md`);
    let sceneData = {};
    try {
      const sceneCardContent = await fs.readFile(sceneCardPath, 'utf8');
      const frontmatterMatch = sceneCardContent.match(/^---\n([\s\S]*?)\n---/);
      if (frontmatterMatch) {
        sceneData = parseSimpleYaml(frontmatterMatch[1]);
      }
    } catch (e) {
      console.warn('无法读取场景卡片:', sceneId);
    }

    // 4. 构建分析prompt
    const analysisPrompt = buildAnalysisPrompt(sceneData, content);

    // 5. 调用AI进行分析
    const completion = await openai.chat.completions.create({
      model: config.analysis_api.model || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的小说分析助手。请分析提供的场景内容，提取关键的状态变化信息，并以YAML格式返回结构化的摘要。'
        },
        {
          role: 'user',
          content: analysisPrompt
        }
      ],
      temperature: config.analysis_api.temperature || 0.3
    });

    const analysisResult = completion.choices[0].message.content;

    // 6. 解析AI返回的YAML
    let summary;
    // 提取YAML部分
    // 先尝试直接解析
    try {
      summary = yaml.load(analysisResult);
    }
    catch (directParseError) {
      // 如果直接解析失败，尝试提取YAML代码块
      const yamlMatch = analysisResult.match(/```yaml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```yml\n([\s\S]*?)\n```/) ||
                       analysisResult.match(/```\n([\s\S]*?)\n```/);
      
      if (yamlMatch) {
        try {
          summary = yaml.load(yamlMatch[1]);
        } catch (blockParseError) {
          console.error('解析YAML代码块失败:', blockParseError);
          return { success: false, error: '无法解析AI分析结果' };
        }
      } else {
        console.error('AI响应中未找到有效的YAML，原始响应：', analysisResult);
        return { success: false, error: '无法解析AI分析结果' };
      }
    }

    // 7. 更新角色状态
    if (summary.state_updates) {
      await updateCharacterStates(projectPath, summary.state_updates);
    }

    // 8. 保存分析摘要
    const summaryPath = path.join(projectPath, '4_summaries', `${sceneId}.yml`);
    await fs.writeFile(summaryPath, yaml.dump(summary, { indent: 2 }), 'utf8');

    return {
      success: true,
      summary,
      summaryPath
    };

  } catch (error) {
    console.error('AI分析失败:', error);
    return { success: false, error: error.message };
  }
}

// 构建分析prompt
function buildAnalysisPrompt(sceneData, content) {
  let prompt = `请分析以下场景内容，提取关键信息并生成结构化摘要：

## 场景信息
- 场景ID: ${sceneData.scene_id || '未知'}
- 故事线: ${sceneData.storyline || '未知'}
- 参与角色: ${sceneData.characters ? sceneData.characters.join(', ') : '未知'}
- 地点: ${sceneData.location || '未知'}

## 场景内容
${content}

## 分析要求
请以YAML格式返回分析结果，包含以下信息：

\`\`\`yaml
scene_summary:
  key_events: ["关键事件1", "关键事件2"]
  emotional_tone: "整体情感基调"
  plot_advancement: "剧情推进情况"
  
state_updates:
  角色名称1:
    emotion: "新的情绪状态"
    location: "新的位置"
    current_status: "当前状态描述"
    inventory_add: ["新增物品、技能、能力"]
    inventory_remove: ["失去的物品、技能、能力"]
    # 只有发生变化的状态才需要更新
  角色名称2:
    emotion: "新的情绪状态"
    # 可以只更新部分状态
    
new_information:
  revealed_secrets: ["揭示的秘密"]
  new_relationships: ["新的关系"]
  world_building: ["世界观扩展"]
\`\`\`

注意：
1. 只更新在场景中明确发生变化的角色状态
2. 情绪状态应该反映角色在场景结束时的心理状态
3. 当前状态更新应该反映角色在场景结束时的身体、心理等状况
4. 位置更新应该基于角色在场景中的移动
5、物品、能力、技能更新应该反映角色在场景结束时的新增或失去
6. 关键事件应该简洁明了，突出重要情节点`;

  return prompt;
}

// 更新角色状态
async function updateCharacterStates(projectPath, stateUpdates) {
  for (const [characterName, updates] of Object.entries(stateUpdates)) {
    try {
      // 根据角色名称查找对应的文件
      const charactersDir = path.join(projectPath, '1_knowledge_base', 'characters');
      let characterPath = null;
      let characterData = null;

      try {
        const characterFiles = await fs.readdir(charactersDir);
        for (const file of characterFiles.filter(f => f.endsWith('.yml'))) {
          const filePath = path.join(charactersDir, file);
          const fileContent = await fs.readFile(filePath, 'utf8');
          const data = yaml.load(fileContent);
          if (data.name === characterName) {
            characterPath = filePath;
            characterData = data;
            break;
          }
        }
      } catch (e) {
        console.warn(`无法读取角色目录`);
        continue;
      }

      if (!characterData) {
        console.warn(`角色不存在: ${characterName}`);
        continue;
      }

      // 更新状态
      if (!characterData.state) {
        characterData.state = {};
      }

      if (updates.emotion) {
        characterData.state.emotion = updates.emotion;
      }
      if (updates.location) {
        characterData.state.location = updates.location;
      }
      if (updates.current_status) {
        characterData.state.current_status = updates.current_status;
      }

      if (updates.inventory_add) {
        if (!characterData.state.inventory) {
          characterData.state.inventory = [];
        }
        characterData.state.inventory.push(...updates.inventory_add);
      }
      if (updates.inventory_remove) {
        if (characterData.state.inventory) {
          characterData.state.inventory = characterData.state.inventory.filter(item => !updates.inventory_remove.includes(item));
        }
      }

      // 保存更新后的角色数据
      await fs.writeFile(characterPath, yaml.dump(characterData, { indent: 2 }), 'utf8');
      console.log(`已更新角色状态: ${characterName}`);

    } catch (error) {
      console.error(`更新角色状态失败: ${characterName}`, error);
    }
  }
}

// 简单的YAML解析器（用于解析frontmatter）
function parseSimpleYaml(yamlContent) {
  const result = {};
  const lines = yamlContent.split('\n');
  let currentKey = null;
  let currentArray = null;

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith('#')) continue;

    if (trimmedLine.startsWith('- ')) {
      // 数组项
      if (currentArray) {
        currentArray.push(trimmedLine.substring(2).trim());
      }
    } else if (trimmedLine.includes(':')) {
      // 键值对
      const [key, ...valueParts] = trimmedLine.split(':');
      const value = valueParts.join(':').trim();
      
      if (value === '') {
        // 可能是数组的开始
        currentKey = key.trim();
        currentArray = [];
        result[currentKey] = currentArray;
      } else if (value.startsWith('[') && value.endsWith(']')) {
        // 内联数组
        const arrayContent = value.slice(1, -1);
        result[key.trim()] = arrayContent.split(',').map(item => item.trim().replace(/['"]/g, ''));
        currentArray = null;
      } else {
        // 普通值
        result[key.trim()] = value.replace(/['"]/g, '');
        currentArray = null;
      }
    }
  }

  return result;
}

module.exports = {
  analyzeSceneAndUpdateState,
  buildAnalysisPrompt,
  updateCharacterStates,
  parseSimpleYaml
};

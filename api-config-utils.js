const { OpenAI } = require('openai');

/**
 * API配置工具函数
 * 提供统一的API配置获取和OpenAI客户端初始化功能
 */

/**
 * 获取API配置
 * 支持新的ai_apis配置格式，同时保持向后兼容性
 * 
 * @param {Object} config - 项目配置对象
 * @param {string} apiType - API类型 (primary_generation, secondary_generation, analysis, planning等)
 * @returns {Object} API配置对象
 */
function getApiConfig(config, apiType = 'primary_generation') {
  // 1. 优先使用新的ai_apis配置格式
  if (config.ai_apis && config.ai_apis[apiType] && config.ai_apis[apiType].enabled) {
    return config.ai_apis[apiType];
  }
  
  // 2. 如果指定类型不可用，尝试使用primary_generation作为备选
  if (config.ai_apis && config.ai_apis.primary_generation && config.ai_apis.primary_generation.enabled) {
    return config.ai_apis.primary_generation;
  }
  
  // 3. 向后兼容：根据API类型映射到旧的配置格式
  if (apiType === 'analysis' && config.analysis_api) {
    return config.analysis_api;
  }
  
  if ((apiType === 'primary_generation' || apiType === 'secondary_generation' || apiType === 'planning') && config.generation_api) {
    return config.generation_api;
  }
  
  // 4. 最后的备选：使用任何可用的API配置
  if (config.generation_api) {
    return config.generation_api;
  }
  
  if (config.analysis_api) {
    return config.analysis_api;
  }
  
  throw new Error(`未找到可用的API配置 (请求类型: ${apiType})`);
}

/**
 * 初始化OpenAI客户端
 * 
 * @param {Object} config - 项目配置对象
 * @param {string} apiType - API类型
 * @returns {OpenAI} OpenAI客户端实例
 */
function initializeOpenAI(config, apiType = 'primary_generation') {
  const apiConfig = getApiConfig(config, apiType);
  
  const openaiConfig = {
    apiKey: apiConfig.api_key
  };

  if (apiConfig.base_url && apiConfig.base_url !== 'https://api.openai.com/v1') {
    openaiConfig.baseURL = apiConfig.base_url;
  }

  return new OpenAI(openaiConfig);
}

/**
 * 验证API配置是否有效
 * 
 * @param {Object} apiConfig - API配置对象
 * @returns {boolean} 配置是否有效
 */
function validateApiConfig(apiConfig) {
  return !!(apiConfig && apiConfig.api_key && apiConfig.api_key.trim());
}

/**
 * 获取所有可用的API配置类型
 * 
 * @param {Object} config - 项目配置对象
 * @returns {Array} 可用的API类型列表
 */
function getAvailableApiTypes(config) {
  const availableTypes = [];
  
  if (config.ai_apis) {
    Object.keys(config.ai_apis).forEach(type => {
      if (config.ai_apis[type] && config.ai_apis[type].enabled && validateApiConfig(config.ai_apis[type])) {
        availableTypes.push(type);
      }
    });
  }
  
  // 向后兼容检查
  if (config.analysis_api && validateApiConfig(config.analysis_api)) {
    if (!availableTypes.includes('analysis')) {
      availableTypes.push('analysis');
    }
  }
  
  if (config.generation_api && validateApiConfig(config.generation_api)) {
    if (!availableTypes.includes('primary_generation')) {
      availableTypes.push('primary_generation');
    }
  }
  
  return availableTypes;
}

/**
 * 创建默认的AI APIs配置结构
 * 
 * @returns {Object} 默认的ai_apis配置
 */
function createDefaultAiApisConfig() {
  return {
    primary_generation: {
      enabled: true,
      base_url: 'https://api.openai.com/v1',
      api_key: '',
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      description: '主要生成API - 用于场景初稿生成'
    },
    secondary_generation: {
      enabled: false,
      base_url: 'https://api.openai.com/v1',
      api_key: '',
      model: 'gpt-3.5-turbo',
      temperature: 0.8,
      description: '辅助生成API - 用于内容优化和重写'
    },
    analysis: {
      enabled: true,
      base_url: 'https://api.openai.com/v1',
      api_key: '',
      model: 'gpt-3.5-turbo',
      temperature: 0.3,
      description: '分析API - 用于场景分析和状态更新'
    },
    planning: {
      enabled: false,
      base_url: 'https://api.openai.com/v1',
      api_key: '',
      model: 'gpt-4',
      temperature: 0.5,
      description: '规划API - 用于大纲生成和情节规划'
    }
  };
}

module.exports = {
  getApiConfig,
  initializeOpenAI,
  validateApiConfig,
  getAvailableApiTypes,
  createDefaultAiApisConfig
};

const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');

// 引入AI功能模块
const { aiCreateScene } = require('./ai-scene-creator');
const { generateSceneDraft } = require('./ai-draft-generator');
const { analyzeSceneAndUpdateState } = require('./ai-state-updater');

// 引入新的AI模块
const { generateMainOutline, generateVolumeOutline } = require('./ai-outline-generator');
const { initializeForeshadowingManager, analyzeSceneForForeshadowing, getPayoffOpportunities } = require('./foreshadowing-manager');
const { startAutoWriting, initializeAutoWriter } = require('./auto-writer');
const { initializeWorkflow, startCompleteWorkflow, getWorkflowStatus } = require('./workflow-manager');

// 保持对窗口对象的全局引用，如果不这样做，当JavaScript对象被垃圾回收时，窗口会被自动关闭
let mainWindow;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false
  });

  // 加载应用的index.html
  mainWindow.loadFile('renderer/index.html');

  // 当窗口准备好显示时显示窗口
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，通常会把多个window对象存放在一个数组里面，与此同时，你应该删除相应的元素
    mainWindow = null;
  });

  // 开发模式下打开开发者工具
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// Electron会在初始化后并准备创建浏览器窗口时，调用这个函数
app.whenReady().then(createWindow);

// 当全部窗口关闭时退出
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，否则绝大部分应用及其菜单栏会保持激活
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，通常在应用程序中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 创建应用菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建项目',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-project');
          }
        },
        {
          label: '打开项目',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-open-project');
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' }
      ]
    },
    {
      label: '视图',
      submenu: [
        { role: 'reload', label: '重新加载' },
        { role: 'forceReload', label: '强制重新加载' },
        { role: 'toggleDevTools', label: '切换开发者工具' },
        { type: 'separator' },
        { role: 'resetZoom', label: '实际大小' },
        { role: 'zoomIn', label: '放大' },
        { role: 'zoomOut', label: '缩小' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: '切换全屏' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于 Chronicler',
              message: 'Chronicler v1.0.0',
              detail: 'AI小说自动化生成系统\n\n基于约定优于配置的原则，让创作者专注于故事本身。'
            });
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// 当应用准备就绪时创建菜单
app.whenReady().then(() => {
  createMenu();
});

// IPC处理程序
ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    await fs.writeFile(filePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-yaml', async (event, filePath) => {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    const parsed = yaml.load(data);
    return { success: true, data: parsed };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-yaml', async (event, filePath, data) => {
  try {
    const yamlStr = yaml.dump(data, { indent: 2 });
    await fs.writeFile(filePath, yamlStr, 'utf8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('create-directory', async (event, dirPath) => {
  try {
    await fs.mkdir(dirPath, { recursive: true });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('check-path-exists', async (event, path) => {
  try {
    await fs.access(path);
    return { success: true, exists: true };
  } catch (error) {
    return { success: true, exists: false };
  }
});

ipcMain.handle('list-directory', async (event, dirPath) => {
  try {
    const files = await fs.readdir(dirPath, { withFileTypes: true });
    const result = files.map(file => ({
      name: file.name,
      isDirectory: file.isDirectory(),
      isFile: file.isFile()
    }));
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-file', async (event, filePath) => {
  try {
    await fs.unlink(filePath);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// AI生成相关的处理程序
ipcMain.handle('generate-scene-draft', async (event, projectPath, sceneData, options) => {
  try {
    const result = await generateSceneDraft(projectPath, sceneData, options);
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('analyze-and-update-state', async (event, projectPath, sceneId, content) => {
  try {
    const result = await analyzeSceneAndUpdateState(projectPath, sceneId, content);
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-create-scene', async (event, projectPath, userParams) => {
  try {
    console.log('收到AI创建场景请求，项目路径:', projectPath);
    console.log('用户参数:', userParams);

    // 确保userParams是一个纯净的对象
    const cleanUserParams = userParams ? JSON.parse(JSON.stringify(userParams)) : {};
    console.log('清理后的用户参数:', cleanUserParams);

    const result = await aiCreateScene(projectPath, cleanUserParams);
    console.log('AI创建场景结果:', result);

    // 确保返回值也是纯净的
    const cleanResult = JSON.parse(JSON.stringify(result));
    console.log('清理后的结果:', cleanResult);

    return cleanResult;
  } catch (error) {
    console.error('AI创建场景IPC处理失败:', error);
    return { success: false, error: error.message };
  }
});

// 新增的IPC处理程序

// 大纲生成相关
ipcMain.handle('generate-main-outline', async (event, projectPath, requirements) => {
  try {
    const result = await generateMainOutline(projectPath, requirements);
    return result;
  } catch (error) {
    console.error('生成主线大纲失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('generate-volume-outline', async (event, projectPath, volumeId, guidance) => {
  try {
    const result = await generateVolumeOutline(projectPath, volumeId, guidance);
    return result;
  } catch (error) {
    console.error('生成分卷细纲失败:', error);
    return { success: false, error: error.message };
  }
});

// 伏笔管理相关
ipcMain.handle('initialize-foreshadowing', async (event, projectPath) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    return { success: true, message: '伏笔管理系统初始化成功' };
  } catch (error) {
    console.error('初始化伏笔管理失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('analyze-scene-foreshadowing', async (event, projectPath, sceneId, content) => {
  try {
    const result = await analyzeSceneForForeshadowing(projectPath, sceneId, content);
    return result;
  } catch (error) {
    console.error('分析场景伏笔失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('get-payoff-opportunities', async (event, projectPath, sceneData) => {
  try {
    const result = await getPayoffOpportunities(projectPath, sceneData);
    return result;
  } catch (error) {
    console.error('获取伏笔回收机会失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('load-foreshadowing-data', async (event, projectPath) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const data = await manager.loadForeshadowingData();
    return { success: true, data };
  } catch (error) {
    console.error('加载伏笔数据失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('add-foreshadowing', async (event, projectPath, foreshadowingList) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const result = await manager.addForeshadowing('manual', foreshadowingList);
    return result;
  } catch (error) {
    console.error('添加伏笔失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('update-foreshadowing', async (event, projectPath, foreshadowingId, updateData) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const result = await manager.updateForeshadowing(foreshadowingId, updateData);
    return result;
  } catch (error) {
    console.error('更新伏笔失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('delete-foreshadowing', async (event, projectPath, foreshadowingId) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const result = await manager.deleteForeshadowing(foreshadowingId);
    return result;
  } catch (error) {
    console.error('删除伏笔失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('mark-foreshadowing-resolved', async (event, projectPath, foreshadowingId, sceneId, resolutionDetails) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const result = await manager.markForeshadowingResolved(foreshadowingId, sceneId, resolutionDetails);
    return result;
  } catch (error) {
    console.error('标记伏笔回收失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('activate-planned-foreshadowing', async (event, projectPath, foreshadowingId) => {
  try {
    const manager = await initializeForeshadowingManager(projectPath);
    const result = await manager.activatePlannedForeshadowing(foreshadowingId);
    return result;
  } catch (error) {
    console.error('激活计划伏笔失败:', error);
    return { success: false, error: error.message };
  }
});

// 全自动创作相关
ipcMain.handle('start-auto-writing', async (event, projectPath, config) => {
  try {
    console.log('启动全自动创作会话，项目路径:', projectPath);
    console.log('配置:', config);
    
    const result = await startAutoWriting(projectPath, config);
    return result;
  } catch (error) {
    console.error('启动全自动创作失败:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('initialize-auto-writer', async (event, projectPath) => {
  try {
    const autoWriter = await initializeAutoWriter(projectPath);
    return { success: true, message: '全自动创作系统初始化成功' };
  } catch (error) {
    console.error('初始化全自动创作系统失败:', error);
    return { success: false, error: error.message };
  }
});

// ========== 工作流程管理相关IPC处理程序 ==========

// 启动完整创作工作流程
ipcMain.handle('start-complete-workflow', async (event, projectPath, config) => {
  try {
    console.log('启动完整创作工作流程，项目路径:', projectPath);
    console.log('工作流程配置:', config);
    
    const result = await startCompleteWorkflow(projectPath, config);
    return result;
  } catch (error) {
    console.error('启动完整创作工作流程失败:', error);
    return { success: false, error: error.message };
  }
});

// 获取工作流程状态
ipcMain.handle('get-workflow-status', async (event, projectPath) => {
  try {
    const result = await getWorkflowStatus(projectPath);
    return result;
  } catch (error) {
    console.error('获取工作流程状态失败:', error);
    return { success: false, error: error.message };
  }
});

// 确认主线大纲
ipcMain.handle('confirm-main-outline', async (event, projectPath, userEdits) => {
  try {
    console.log('确认主线大纲，项目路径:', projectPath);
    
    const { WorkflowManager } = require('./workflow-manager');
    const manager = new WorkflowManager(projectPath);
    await manager.initialize();
    
    const result = await manager.confirmMainOutline(userEdits);
    return result;
  } catch (error) {
    console.error('确认主线大纲失败:', error);
    return { success: false, error: error.message };
  }
});

// 确认分卷细纲
ipcMain.handle('confirm-volume-outlines', async (event, projectPath, userEdits) => {
  try {
    console.log('确认分卷细纲，项目路径:', projectPath);
    
    const { WorkflowManager } = require('./workflow-manager');
    const manager = new WorkflowManager(projectPath);
    await manager.initialize();
    
    const result = await manager.confirmVolumeOutlines(userEdits);
    return result;
  } catch (error) {
    console.error('确认分卷细纲失败:', error);
    return { success: false, error: error.message };
  }
});

// 开始分卷创作
ipcMain.handle('start-volume-writing', async (event, projectPath, volumeId, writingConfig) => {
  try {
    console.log(`开始分卷${volumeId}创作，项目路径:`, projectPath);
    console.log('创作配置:', writingConfig);
    
    const { WorkflowManager } = require('./workflow-manager');
    const manager = new WorkflowManager(projectPath);
    await manager.initialize();
    
    const result = await manager.startVolumeWriting(volumeId, writingConfig);
    return result;
  } catch (error) {
    console.error(`开始分卷${volumeId}创作失败:`, error);
    return { success: false, error: error.message };
  }
});

// 暂停工作流程
ipcMain.handle('pause-workflow', async (event, projectPath, reason) => {
  try {
    const { WorkflowManager } = require('./workflow-manager');
    const manager = new WorkflowManager(projectPath);
    await manager.initialize();
    
    const result = await manager.pauseWorkflow(reason);
    return result;
  } catch (error) {
    console.error('暂停工作流程失败:', error);
    return { success: false, error: error.message };
  }
});

// 恢复工作流程
ipcMain.handle('resume-workflow', async (event, projectPath) => {
  try {
    const { WorkflowManager } = require('./workflow-manager');
    const manager = new WorkflowManager(projectPath);
    await manager.initialize();
    
    const result = await manager.resumeWorkflow();
    return result;
  } catch (error) {
    console.error('恢复工作流程失败:', error);
    return { success: false, error: error.message };
  }
});

// 初始化工作流程管理器
ipcMain.handle('initialize-workflow', async (event, projectPath) => {
  try {
    const manager = await initializeWorkflow(projectPath);
    return { success: true, message: '工作流程管理器初始化成功' };
  } catch (error) {
    console.error('初始化工作流程管理器失败:', error);
    return { success: false, error: error.message };
  }
});


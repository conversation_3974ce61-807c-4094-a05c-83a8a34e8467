const path = require('path');
const fs = require('fs').promises;
const yaml = require('js-yaml');
const { OpenAI } = require('openai');

// 引入其他AI模块
const { AIOutlineGenerator } = require('./ai-outline-generator');
const { ForeshadowingManager } = require('./foreshadowing-manager');
const { aiCreateScene } = require('./ai-scene-creator');
const { generateSceneDraft } = require('./ai-draft-generator');
const { analyzeSceneAndUpdateState } = require('./ai-state-updater');

/**
 * 全自动创作模式 - 整合所有AI模块实现全流程自动化
 */
class AutoWriter {
  constructor(projectPath) {
    this.projectPath = projectPath;
    this.outlineGenerator = new AIOutlineGenerator(projectPath);
    this.foreshadowingManager = new ForeshadowingManager(projectPath);
    this.autoWriterPath = path.join(projectPath, '6_automation');
    this.sessionPath = path.join(this.autoWriterPath, 'current_session.yml');
    this.logPath = path.join(this.autoWriterPath, 'automation_log.yml');
  }

  /**
   * 初始化全自动创作模式
   */
  async initialize() {
    await this.ensureDirectoryExists(this.autoWriterPath);
    await this.foreshadowingManager.initialize();
    
    // 初始化会话文件
    if (!await this.fileExists(this.sessionPath)) {
      const initialSession = {
        session_id: this.generateSessionId(),
        started_at: new Date().toISOString(),
        status: 'idle',
        current_volume: null,
        current_chapter: null,
        automation_settings: {
          auto_outline: true,
          auto_scene_creation: true,
          auto_draft_generation: true,
          auto_foreshadowing: true,
          auto_state_update: true,
          chapters_per_session: 5,
          words_per_chapter: 1500
        },
        progress: {
          completed_chapters: 0,
          total_planned_chapters: 0,
          current_volume_progress: 0
        }
      };
      await fs.writeFile(this.sessionPath, yaml.dump(initialSession, { indent: 2 }), 'utf8');
    }
  }

  /**
   * 启动全自动创作会话
   */
  async startAutoWritingSession(sessionConfig = {}) {
    try {
      console.log('🚀 启动全自动创作会话...');
      
      // 更新会话配置
      const session = await this.loadSession();
      session.status = 'running';
      session.started_at = new Date().toISOString();
      session.session_id = this.generateSessionId();
      
      // 合并用户配置
      Object.assign(session.automation_settings, sessionConfig);
      await this.saveSession(session);
      
      // 记录开始日志
      await this.logActivity('session_started', { 
        session_id: session.session_id,
        config: session.automation_settings 
      });

      let result = { success: true, steps: [] };

      // 步骤1: 检查并生成主线大纲
      if (session.automation_settings.auto_outline) {
        console.log('📋 检查主线大纲...');
        const outlineResult = await this.ensureMainOutline(sessionConfig.outline_requirements);
        result.steps.push({ step: 'main_outline', ...outlineResult });
        
        if (!outlineResult.success) {
          return this.handleError('主线大纲生成失败', outlineResult.error);
        }
      }

      // 步骤2: 确定当前工作分卷
      console.log('📚 确定工作分卷...');
      const volumeResult = await this.determineCurrentVolume();
      result.steps.push({ step: 'determine_volume', ...volumeResult });
      
      if (!volumeResult.success) {
        return this.handleError('确定工作分卷失败', volumeResult.error);
      }

      session.current_volume = volumeResult.volumeId;
      await this.saveSession(session);

      // 步骤3: 生成分卷细纲
      if (session.automation_settings.auto_outline) {
        console.log('📖 生成分卷细纲...');
        const volumeOutlineResult = await this.ensureVolumeOutline(volumeResult.volumeId, sessionConfig.volume_guidance);
        result.steps.push({ step: 'volume_outline', ...volumeOutlineResult });
        
        if (!volumeOutlineResult.success) {
          return this.handleError('分卷细纲生成失败', volumeOutlineResult.error);
        }
      }

      // 步骤4: 开始章节创作循环
      console.log('✍️ 开始自动创作循环...');
      const writingResult = await this.startWritingLoop(session);
      result.steps.push({ step: 'writing_loop', ...writingResult });

      // 更新会话状态
      session.status = writingResult.success ? 'completed' : 'error';
      session.completed_at = new Date().toISOString();
      await this.saveSession(session);

      await this.logActivity('session_completed', {
        session_id: session.session_id,
        success: writingResult.success,
        chapters_created: writingResult.chapters_created || 0
      });

      return result;

    } catch (error) {
      console.error('全自动创作会话失败:', error);
      return this.handleError('创作会话失败', error.message);
    }
  }

  /**
   * 创作循环 - 核心自动化逻辑
   */
  async startWritingLoop(session) {
    const maxChapters = session.automation_settings.chapters_per_session;
    let chaptersCreated = 0;
    const results = [];

    try {
      for (let i = 0; i < maxChapters; i++) {
        console.log(`📝 创作第 ${i + 1} 章...`);
        
        // 单章创作流程
        const chapterResult = await this.createSingleChapter(session);
        results.push(chapterResult);
        
        if (chapterResult.success) {
          chaptersCreated++;
          session.progress.completed_chapters++;
          await this.saveSession(session);
          
          console.log(`✅ 第 ${chaptersCreated} 章创作完成: ${chapterResult.scene_id}`);
          
          // 短暂延迟，避免API频率限制
          await this.delay(2000);
        } else {
          console.log(`❌ 第 ${i + 1} 章创作失败: ${chapterResult.error}`);
          
          // 如果连续失败3次，终止会话
          const recentFailures = results.slice(-3).filter(r => !r.success).length;
          if (recentFailures >= 3) {
            console.log('🛑 连续失败过多，终止创作会话');
            break;
          }
        }
      }

      return {
        success: true,
        chapters_created: chaptersCreated,
        results
      };

    } catch (error) {
      console.error('创作循环失败:', error);
      return {
        success: false,
        error: error.message,
        chapters_created: chaptersCreated,
        results
      };
    }
  }

  /**
   * 创建单个章节的完整流程
   */
  async createSingleChapter(session) {
    try {
      const steps = [];

      // 步骤1: 创建场景卡片
      if (session.automation_settings.auto_scene_creation) {
        console.log('🎬 创建场景卡片...');
        const sceneResult = await this.createSceneCard(session);
        steps.push({ step: 'scene_creation', ...sceneResult });
        
        if (!sceneResult.success) {
          return { success: false, error: '场景卡片创建失败', steps };
        }
        
        session.current_chapter = sceneResult.sceneData.scene_id;
      }

      // 步骤2: 分析伏笔机会
      let foreshadowingContext = {};
      if (session.automation_settings.auto_foreshadowing) {
        console.log('🔮 分析伏笔机会...');
        const foreshadowingResult = await this.analyzeForeshadowingOpportunities(session.current_chapter);
        steps.push({ step: 'foreshadowing_analysis', ...foreshadowingResult });
        foreshadowingContext = foreshadowingResult.data || {};
      }

      // 步骤3: 生成章节初稿
      if (session.automation_settings.auto_draft_generation) {
        console.log('📄 生成章节初稿...');
        const draftResult = await this.generateChapterDraft(session.current_chapter, foreshadowingContext);
        steps.push({ step: 'draft_generation', ...draftResult });
        
        if (!draftResult.success) {
          return { success: false, error: '初稿生成失败', steps };
        }
      }

      // 检查是否使用了合并分析功能
      const usedCombinedAnalysis = draftResult.combinedAnalysis;



      // 步骤5: 更新角色状态（如果未使用合并分析）
      if (session.automation_settings.auto_state_update && !usedCombinedAnalysis) {
        console.log('🔄 更新角色状态...');
        const stateResult = await this.updateCharacterStates(session.current_chapter);
        steps.push({ step: 'state_update', ...stateResult });
      } else if (usedCombinedAnalysis) {
        console.log('✅ 角色状态已在合并分析中更新');
        steps.push({ 
          step: 'state_update', 
          success: true, 
          message: '已通过合并分析更新',
          updates: usedCombinedAnalysis.character_updates 
        });
      }

      // 步骤6: 处理伏笔（如果未使用合并分析）
      if (session.automation_settings.auto_foreshadowing && !usedCombinedAnalysis) {
        console.log('🎭 处理伏笔...');
        const foreshadowingProcessResult = await this.processForeshadowing(session.current_chapter);
        steps.push({ step: 'foreshadowing_process', ...foreshadowingProcessResult });
      } else if (usedCombinedAnalysis) {
        console.log('✅ 伏笔分析已在合并分析中完成');
        steps.push({ 
          step: 'foreshadowing_process', 
          success: true, 
          message: '已通过合并分析处理',
          new_foreshadowing: usedCombinedAnalysis.new_foreshadowing_count 
        });
      }

      await this.logActivity('chapter_completed', {
        scene_id: session.current_chapter,
        volume_id: session.current_volume,
        steps: steps.map(s => ({ step: s.step, success: s.success }))
      });

      return {
        success: true,
        scene_id: session.current_chapter,
        steps
      };

    } catch (error) {
      console.error('单章创作失败:', error);
      return {
        success: false,
        error: error.message,
        scene_id: session.current_chapter
      };
    }
  }

  /**
   * 确保主线大纲存在
   */
  async ensureMainOutline(requirements = {}) {
    try {
      const outlinePath = path.join(this.projectPath, '2_plot', 'main_outline.md');
      
      // 检查是否已存在主线大纲
      try {
        const existingOutline = await fs.readFile(outlinePath, 'utf8');
        if (existingOutline.trim().length > 100) { // 大纲内容足够长
          return { success: true, exists: true, path: outlinePath };
        }
      } catch (e) {
        // 文件不存在，需要生成
      }

      // 生成主线大纲
      console.log('生成新的主线大纲...');
      const result = await this.outlineGenerator.generateMainOutline(requirements);
      
      return {
        success: result.success,
        generated: true,
        path: result.outlinePath,
        volumes: result.volumes,
        error: result.error
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 确定当前工作分卷
   */
  async determineCurrentVolume() {
    try {
      // 检查已发布章节数量
      const publishedDir = path.join(this.projectPath, '3_manuscript', 'published');
      let publishedCount = 0;
      
      try {
        const publishedFiles = await fs.readdir(publishedDir);
        publishedCount = publishedFiles.filter(f => f.endsWith('.md')).length;
      } catch (e) {
        // 目录不存在
      }

      // 根据已发布章节数确定当前分卷
      let currentVolumeId;
      if (publishedCount <= 25) {
        currentVolumeId = 'vol_1';
      } else if (publishedCount <= 50) {
        currentVolumeId = 'vol_2';
      } else if (publishedCount <= 75) {
        currentVolumeId = 'vol_3';
      } else if (publishedCount <= 100) {
        currentVolumeId = 'vol_4';
      } else {
        currentVolumeId = 'vol_5';
      }

      return {
        success: true,
        volumeId: currentVolumeId,
        publishedCount
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 确保分卷细纲存在
   */
  async ensureVolumeOutline(volumeId, guidance = {}) {
    try {
      const volumeOutlinePath = path.join(this.projectPath, '2_plot', 'volumes', volumeId, 'outline.md');
      
      // 检查是否已存在分卷细纲
      try {
        const existingOutline = await fs.readFile(volumeOutlinePath, 'utf8');
        if (existingOutline.trim().length > 100) {
          return { success: true, exists: true, path: volumeOutlinePath };
        }
      } catch (e) {
        // 文件不存在，需要生成
      }

      // 生成分卷细纲
      console.log(`生成第${volumeId}卷细纲...`);
      const result = await this.outlineGenerator.generateVolumeOutline(volumeId, guidance);
      
      return {
        success: result.success,
        generated: true,
        path: result.volumeOutlinePath,
        chapters: result.chapters,
        error: result.error
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建场景卡片
   */
  async createSceneCard(session) {
    try {
      // 智能确定下一个场景
      const nextSceneParams = await this.determineNextScene(session);
      
      const result = await aiCreateScene(this.projectPath, nextSceneParams);
      
      if (result.success) {
        return {
          success: true,
          sceneData: result.sceneData,
          generated: true
        };
      } else {
        return { success: false, error: result.error };
      }

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 确定下一个场景参数
   */
  async determineNextScene(session) {
    try {
      // 获取当前分卷细纲
      const volumeOutline = await this.outlineGenerator.getCurrentVolumeOutline(`${session.current_volume}_temp`);
      
      // 分析已发布章节
      const publishedChapters = await this.getPublishedChapters();
      const nextChapterNumber = publishedChapters.length + 1;
      
      // 智能生成场景参数
      return {
        scene_id: `第${nextChapterNumber}章`,
        volume_context: volumeOutline?.content,
        chapter_number: nextChapterNumber,
        auto_determine: true
      };

    } catch (error) {
      console.error('确定下一个场景失败:', error);
      return {
        scene_id: `第${Date.now()}章_自动生成`,
        auto_determine: true
      };
    }
  }

  /**
   * 分析伏笔机会
   */
  async analyzeForeshadowingOpportunities(sceneId) {
    try {
      // 获取场景数据
      const sceneData = await this.getSceneData(sceneId);
      if (!sceneData) {
        return { success: false, error: '无法获取场景数据' };
      }

      // 获取回收机会
      const payoffResult = await this.foreshadowingManager.getPayoffOpportunities(sceneData);
      
      // 获取新伏笔建议
      const suggestionResult = await this.foreshadowingManager.generateForeshadowingSuggestions(
        sceneData, 
        { worldBible: await this.loadWorldBible() }
      );

      return {
        success: true,
        data: {
          payoffOpportunities: payoffResult.opportunities || [],
          suggestions: suggestionResult.suggestions || {}
        }
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 生成章节初稿（增强版，支持合并功能）
   */
  async generateChapterDraft(sceneId, foreshadowingContext) {
    try {
      // 获取场景数据
      const sceneData = await this.getSceneData(sceneId);
      if (!sceneData) {
        return { success: false, error: '无法获取场景数据' };
      }

      // 增强场景数据，加入伏笔信息
      const enhancedSceneData = await this.enhanceSceneDataWithForeshadowing(sceneData, foreshadowingContext);

      // 调用增强版生成函数，启用合并分析
      const result = await generateSceneDraft(this.projectPath, enhancedSceneData, { 
        combinedAnalysis: true 
      });
      
      return result;

    } catch (error) {
      return { success: false, error: error.message };
    }
  }



  /**
   * 更新角色状态
   */
  async updateCharacterStates(sceneId) {
    try {
      const draftPath = path.join(this.projectPath, '3_manuscript', 'drafts', `${sceneId}.md`);
      const content = await fs.readFile(draftPath, 'utf8');
      
      const result = await analyzeSceneAndUpdateState(this.projectPath, sceneId, content);
      
      return result;

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理伏笔
   */
  async processForeshadowing(sceneId) {
    try {
      const draftPath = path.join(this.projectPath, '3_manuscript', 'drafts', `${sceneId}.md`);
      const content = await fs.readFile(draftPath, 'utf8');
      
      // 分析新伏笔
      const analysisResult = await this.foreshadowingManager.analyzeSceneForForeshadowing(sceneId, content);
      
      return {
        success: true,
        newForeshadowing: analysisResult.foreshadowing || [],
        analysisSuccess: analysisResult.success
      };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 辅助方法
   */
  async enhanceSceneDataWithForeshadowing(sceneData, foreshadowingContext) {
    const enhanced = { ...sceneData };
    
    // 添加伏笔信息到场景描述
    if (foreshadowingContext.payoffOpportunities && foreshadowingContext.payoffOpportunities.length > 0) {
      enhanced.foreshadowing_payoffs = foreshadowingContext.payoffOpportunities.slice(0, 2); // 最多2个
    }
    
    if (foreshadowingContext.suggestions) {
      enhanced.foreshadowing_suggestions = foreshadowingContext.suggestions;
    }
    
    return enhanced;
  }

  async getSceneData(sceneId) {
    try {
      const sceneCardPath = path.join(this.projectPath, '2_plot', 'scene_cards', `${sceneId}.md`);
      const content = await fs.readFile(sceneCardPath, 'utf8');
      
      const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
      if (frontmatterMatch) {
        return yaml.load(frontmatterMatch[1]);
      }
      
      return null;
    } catch (error) {
      return null;
    }
  }

  async getPublishedChapters() {
    try {
      const publishedDir = path.join(this.projectPath, '3_manuscript', 'published');
      const files = await fs.readdir(publishedDir);
      return files.filter(f => f.endsWith('.md'));
    } catch (error) {
      return [];
    }
  }

  async loadWorldBible() {
    try {
      const worldBiblePath = path.join(this.projectPath, '1_knowledge_base', 'world_bible.md');
      return await fs.readFile(worldBiblePath, 'utf8');
    } catch (error) {
      return '';
    }
  }



  async loadSession() {
    try {
      const content = await fs.readFile(this.sessionPath, 'utf8');
      return yaml.load(content);
    } catch (error) {
      throw new Error('无法加载会话数据');
    }
  }

  async saveSession(session) {
    await fs.writeFile(this.sessionPath, yaml.dump(session, { indent: 2 }), 'utf8');
  }

  async logActivity(activity, data) {
    try {
      let log = [];
      try {
        const logContent = await fs.readFile(this.logPath, 'utf8');
        log = yaml.load(logContent) || [];
      } catch (e) {
        // 日志文件不存在
      }

      log.push({
        timestamp: new Date().toISOString(),
        activity,
        data
      });

      // 只保留最近1000条日志
      if (log.length > 1000) {
        log = log.slice(-1000);
      }

      await fs.writeFile(this.logPath, yaml.dump(log, { indent: 2 }), 'utf8');
    } catch (error) {
      console.error('记录日志失败:', error);
    }
  }

  handleError(message, error) {
    console.error(`❌ ${message}:`, error);
    return { success: false, error: `${message}: ${error}` };
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * 导出函数接口
 */
async function startAutoWriting(projectPath, config = {}) {
  const autoWriter = new AutoWriter(projectPath);
  await autoWriter.initialize();
  return await autoWriter.startAutoWritingSession(config);
}

async function initializeAutoWriter(projectPath) {
  const autoWriter = new AutoWriter(projectPath);
  await autoWriter.initialize();
  return autoWriter;
}

module.exports = {
  AutoWriter,
  startAutoWriting,
  initializeAutoWriter
}; 
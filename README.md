# Chronicler - AI小说自动化生成系统

基于Electron的桌面应用，严格遵循"约定优于配置"原则，实现AI辅助的小说创作工作流。

## 核心特性

- **文件系统为数据库**: 所有数据存储在项目文件夹中，应用仅作为可视化界面
- **AI驱动的创作流程**: 集成OpenAI API，支持场景生成和状态分析
- **自动化状态管理**: AI自动分析场景内容并更新角色状态
- **结构化创作**: 支持角色、地点、场景卡片的完整管理
- **实时协作**: 人机协作的写作模式，AI处理程式化任务，作者专注创意

## 安装和运行

### 前置要求
- Node.js (推荐 v16 或更高版本)
- npm 或 yarn
- OpenAI API Key

### 安装步骤

1. 安装依赖:
```bash
npm install
```

2. 启动应用:
```bash
npm start
```

3. 开发模式:
```bash
npm run dev
```

4. 构建应用:
```bash
npm run build
```

## 使用指南

### 1. 创建新项目
- 点击"文件" -> "新建项目"
- 选择项目保存位置
- 系统自动创建完整的目录结构

### 2. 配置AI API
- 在项目概览中点击"配置API"
- 输入OpenAI API Key
- 选择合适的模型和参数

### 3. 构建知识库
- **角色管理**: 创建和编辑角色信息，包括动机、外貌、状态等
- **地点管理**: 定义故事发生的场所和环境
- **世界观设定**: 编辑世界观背景文档

### 4. 规划情节
- **场景卡片**: 创建结构化的场景描述
- **目标设定**: 为每个场景定义具体的创作目标
- **角色分配**: 指定参与场景的角色和地点

### 5. AI生成初稿
- 在场景卡片中点击"生成初稿"
- AI自动汇编上下文信息
- 生成符合设定的场景文本

### 6. 编辑和润色
- 在草稿箱中编辑AI生成的内容
- 使用内置编辑器进行修改和润色
- 支持自动保存和格式化工具

### 7. 提交终稿
- 点击"完成并提交"
- AI自动分析场景内容
- 更新角色状态和关系
- 生成机器可读的摘要

## 项目结构

```
my_novel/
├── chronicler.yml              # 项目配置
├── 1_knowledge_base/           # 知识库
│   ├── characters/             # 角色文件
│   ├── locations/              # 地点文件
│   └── world_bible.md          # 世界观设定
├── 2_plot/                     # 情节规划
│   ├── main_outline.md         # 主线大纲
│   └── scene_cards/            # 场景卡片
├── 3_manuscript/               # 手稿
│   ├── drafts/                 # AI生成的初稿
│   └── published/              # 最终发布的章节
└── 4_summaries/                # 机器可读摘要
```

## 技术栈

- **应用框架**: Electron
- **前端**: Vue.js 3, HTML5, CSS3
- **后端**: Node.js
- **数据格式**: YAML, Markdown
- **AI集成**: OpenAI API
- **模板引擎**: Nunjucks

## 开发理念

### 约定优于配置
- 标准化的目录结构
- 统一的文件格式
- 预定义的工作流程

### 人机协作
- AI处理重复性任务
- 人类专注创意和艺术
- 智能状态管理

### 透明性和可控性
- 所有数据存储在文件系统
- 完全的项目可移植性
- 无供应商锁定

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 支持

如有问题，请查看文档或提交Issue。

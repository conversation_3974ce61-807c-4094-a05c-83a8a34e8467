<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chronicler - AI小说自动化生成系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="app-icon">📚</span>
                    Chronicler
                </h1>
                <div class="project-info" v-if="currentProject">
                    <span class="project-name">{{ currentProject.name }}</span>
                    <span class="project-path">{{ currentProject.path }}</span>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="app-main">
            <!-- 左侧导航栏 -->
            <nav class="sidebar">
                <div class="nav-section">
                    <h3 class="nav-title">项目管理</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'dashboard' }" @click="setActiveView('dashboard')">
                            <span class="nav-icon">🏠</span>
                            项目概览
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">知识库</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'characters' }" @click="setActiveView('characters')">
                            <span class="nav-icon">👥</span>
                            角色管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'locations' }" @click="setActiveView('locations')">
                            <span class="nav-icon">🏛️</span>
                            地点管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'organizations' }" @click="setActiveView('organizations')">
                            <span class="nav-icon">🏢</span>
                            组织管理
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'worldbible' }" @click="setActiveView('worldbible')">
                            <span class="nav-icon">🌍</span>
                            世界观设定
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">情节规划</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'outline' }" @click="setActiveView('outline')">
                            <span class="nav-icon">📋</span>
                            主线大纲
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'scenes' }" @click="setActiveView('scenes')">
                            <span class="nav-icon">🎬</span>
                            场景卡片
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'foreshadowing' }" @click="setActiveView('foreshadowing')">
                            <span class="nav-icon">🔮</span>
                            伏笔管理
                        </li>
                    </ul>
                </div>

                <div class="nav-section" v-if="currentProject">
                    <h3 class="nav-title">创作工坊</h3>
                    <ul class="nav-list">
                        <li class="nav-item" :class="{ active: activeView === 'drafts' }" @click="setActiveView('drafts')">
                            <span class="nav-icon">📝</span>
                            草稿箱
                        </li>
                        <li class="nav-item" :class="{ active: activeView === 'published' }" @click="setActiveView('published')">
                            <span class="nav-icon">📖</span>
                            已发布
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="content-area">
                <!-- 项目概览视图 -->
                <div v-if="activeView === 'dashboard'" class="view-container">
                    <div class="view-header">
                        <h2>项目概览</h2>
                    </div>
                    <div class="dashboard-content">
                        <div v-if="!currentProject" class="welcome-section">
                            <div class="welcome-card">
                                <h3>欢迎使用 Chronicler</h3>
                                <p>开始您的AI辅助小说创作之旅</p>
                                <div class="action-buttons">
                                    <button class="btn btn-primary" @click="createNewProject">
                                        <span class="btn-icon">➕</span>
                                        新建项目
                                    </button>
                                    <button class="btn btn-secondary" @click="openExistingProject">
                                        <span class="btn-icon">📂</span>
                                        打开项目
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div v-else class="project-dashboard">
                            <div class="stats-grid">
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.characters }}</div>
                                    <div class="stat-label">角色</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.locations }}</div>
                                    <div class="stat-label">地点</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.organizations }}</div>
                                    <div class="stat-label">组织</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.scenes }}</div>
                                    <div class="stat-label">场景</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.drafts }}</div>
                                    <div class="stat-label">草稿</div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-number">{{ projectStats.published }}</div>
                                    <div class="stat-label">已发布</div>
                                </div>
                            </div>

                            <!-- API配置区域 -->
                            <div class="api-config-section">
                                <h3>AI配置</h3>
                                <div class="config-status">
                                    <span v-if="apiConfigured" class="status-indicator status-ok">✓ API已配置</span>
                                    <span v-else class="status-indicator status-warning">⚠ 需要配置API</span>
                                    <button class="btn btn-secondary btn-small" @click="showApiConfigDialog">
                                        {{ apiConfigured ? '修改配置' : '配置API' }}
                                    </button>
                                </div>
                            </div>

                            <!-- 全自动化创作工作流程 -->
                            <div class="workflow-section">
                                <h3>全自动化创作流程</h3>
                                <div class="workflow-status" v-if="workflowStatus">
                                    <div class="workflow-info">
                                        <span class="workflow-phase">{{ getWorkflowPhaseText(workflowStatus.current_phase) }}</span>
                                        <div class="workflow-progress" v-if="workflowStatus.progress">
                                            <span class="progress-text">
                                                已完成 {{ workflowStatus.progress.completed_volumes }}/{{ workflowStatus.progress.total_volumes }} 卷
                                                ({{ workflowStatus.progress.completed_chapters }}/{{ workflowStatus.progress.total_chapters }} 章)
                                            </span>
                                        </div>
                                    </div>
                                    <div class="workflow-actions">
                                        <button v-if="workflowStatus.current_phase === 'main_outline_review'" 
                                                class="btn btn-primary btn-small" @click="showOutlineReviewDialog = true">
                                            审阅大纲
                                        </button>
                                        <button v-if="workflowStatus.current_phase === 'volume_outlines_review'" 
                                                class="btn btn-primary btn-small" @click="showVolumeReviewDialog = true">
                                            审阅分卷细纲
                                        </button>
                                        <div v-if="workflowStatus.current_phase === 'volume_writing_ready'" class="volume-buttons">
                                            <button v-for="volume in Object.values(workflowStatus.volumes || {})"
                                                    :key="volume.id"
                                                    v-if="!volume.writing_completed"
                                                    class="btn btn-success btn-small"
                                                    @click="startVolumeWriting(volume.id)">
                                                开始第{{ volume.id.replace('vol_', '') }}卷创作
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="workflow-actions">
                                    <button class="btn btn-primary" @click="showWorkflowConfigDialog" :disabled="!apiConfigured">
                                        <span class="btn-icon">🚀</span>
                                        启动全自动化创作
                                    </button>
                                    <button class="btn btn-secondary btn-small" @click="getWorkflowStatus">
                                        <span class="btn-icon">🔄</span>
                                        检查工作流程状态
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 角色管理视图 -->
                <div v-else-if="activeView === 'characters'" class="view-container">
                    <div class="view-header">
                        <h2>角色管理</h2>
                        <button class="btn btn-primary" @click="showCreateCharacterDialog">
                            <span class="btn-icon">➕</span>
                            新建角色
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="characters.length === 0" class="empty-state">
                            <p>还没有创建任何角色</p>
                            <button class="btn btn-primary" @click="showCreateCharacterDialog">创建第一个角色</button>
                        </div>
                        <div v-else class="characters-grid">
                            <div v-for="character in characters" :key="character.name" class="character-card">
                                <div class="character-header">
                                    <h3 class="character-name">
                                        {{ character.name }}
                                        <span v-if="character.ai_generated" class="ai-tag">🤖 AI生成</span>
                                    </h3>
                                    <div class="character-actions">
                                        <button class="btn-icon-small" @click="editCharacter(character)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteCharacter(character)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="character-info">
                                    <p class="character-description">{{ character.description || '未设定描述' }}</p>
                                    <p class="character-motivation">{{ character.motivation || '未设定动机' }}</p>
                                    <div class="character-state">
                                        <span class="state-item">情绪: {{ character.state?.emotion || '未知' }}</span>
                                        <span class="state-item">位置: {{ character.state?.location || '未知' }}</span>
                                        <span class="state-item">当前状态: {{ character.state?.current_status || '未设定' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地点管理视图 -->
                <div v-else-if="activeView === 'locations'" class="view-container">
                    <div class="view-header">
                        <h2>地点管理</h2>
                        <button class="btn btn-primary" @click="showCreateLocationDialog">
                            <span class="btn-icon">➕</span>
                            新建地点
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="locations.length === 0" class="empty-state">
                            <p>还没有创建任何地点</p>
                            <button class="btn btn-primary" @click="showCreateLocationDialog">创建第一个地点</button>
                        </div>
                        <div v-else class="locations-grid">
                            <div v-for="location in locations" :key="location.name" class="location-card">
                                <div class="location-header">
                                    <h3 class="location-name">
                                        {{ location.name }}
                                        <span v-if="location.ai_generated" class="ai-tag">🤖 AI生成</span>
                                    </h3>
                                    <div class="location-actions">
                                        <button class="btn-icon-small" @click="editLocation(location)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteLocation(location)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="location-info">
                                    <p class="location-description">{{ location.description || '暂无描述' }}</p>
                                    <p class="location-type">类型: {{ location.type || '未分类' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 场景卡片管理视图 -->
                <div v-else-if="activeView === 'scenes'" class="view-container">
                    <div class="view-header">
                        <h2>场景卡片</h2>
                        <button class="btn btn-primary" @click="showCreateSceneDialog">
                            <span class="btn-icon">➕</span>
                            新建场景
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="scenes.length === 0" class="empty-state">
                            <p>还没有创建任何场景卡片</p>
                            <button class="btn btn-primary" @click="showCreateSceneDialog">创建第一个场景</button>
                        </div>
                        <div v-else class="scenes-list">
                            <div v-for="scene in scenes" :key="scene.scene_id" class="scene-card">
                                <div class="scene-header">
                                    <div class="scene-info">
                                        <h3 class="scene-title">
                                            {{ scene.scene_id }}
                                            <span v-if="scene.ai_generated" class="ai-tag">🤖 AI生成</span>
                                        </h3>
                                        <span class="scene-storyline">{{ scene.storyline || 'main' }}</span>
                                    </div>
                                    <div class="scene-actions">
                                        <button class="btn btn-small btn-success" @click="generateDraft(scene)"
                                                :disabled="scene.hasDraft" title="生成初稿">
                                            {{ scene.hasDraft ? '已生成' : '生成初稿' }}
                                        </button>
                                        <button class="btn-icon-small" @click="editScene(scene)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteScene(scene)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="scene-details">
                                    <div class="scene-participants">
                                        <strong>角色:</strong>
                                        <span v-if="scene.characters && scene.characters.length > 0">
                                            {{ scene.characters.join(', ') }}
                                        </span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="scene-location">
                                        <strong>地点:</strong>
                                        <span v-if="scene.location">{{ scene.location }}</span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="scene-goals">
                                        <strong>目标:</strong>
                                        <span v-if="scene.goal && scene.goal.trim()">{{ scene.goal }}</span>
                                        <span v-else class="text-muted">未设定目标</span>
                                    </div>
                                    <div class="scene-description" v-if="scene.description">
                                        <strong>简述:</strong>
                                        <p>{{ scene.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 伏笔管理视图 -->
                <div v-else-if="activeView === 'foreshadowing'" class="view-container">
                    <div class="view-header">
                        <h2>伏笔管理</h2>
                        <div class="foreshadowing-actions">
                            <button class="btn btn-secondary" @click="refreshForeshadowingData">
                                <span class="btn-icon">🔄</span>
                                刷新数据
                            </button>
                            <button class="btn btn-primary" @click="showAddForeshadowingDialog">
                                <span class="btn-icon">➕</span>
                                手动添加伏笔
                            </button>
                        </div>
                    </div>
                    <div class="view-content">
                        <!-- 伏笔统计概览 -->
                        <div class="foreshadowing-stats">
                            <div class="stat-card">
                                <div class="stat-number">{{ activeForeshadowing.length }}</div>
                                <div class="stat-label">活跃伏笔</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ resolvedForeshadowing.length }}</div>
                                <div class="stat-label">已回收伏笔</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">{{ plannedForeshadowing.length }}</div>
                                <div class="stat-label">计划伏笔</div>
                            </div>
                        </div>

                        <!-- 伏笔分类标签 -->
                        <div class="foreshadowing-tabs">
                            <button class="tab-btn" :class="{ active: activeForeshadowingTab === 'active' }"
                                    @click="activeForeshadowingTab = 'active'">
                                活跃伏笔 ({{ activeForeshadowing.length }})
                            </button>
                            <button class="tab-btn" :class="{ active: activeForeshadowingTab === 'resolved' }"
                                    @click="activeForeshadowingTab = 'resolved'">
                                已回收 ({{ resolvedForeshadowing.length }})
                            </button>
                            <button class="tab-btn" :class="{ active: activeForeshadowingTab === 'planned' }"
                                    @click="activeForeshadowingTab = 'planned'">
                                计划中 ({{ plannedForeshadowing.length }})
                            </button>
                        </div>

                        <!-- 活跃伏笔列表 -->
                        <div v-if="activeForeshadowingTab === 'active'" class="foreshadowing-list">
                            <div v-if="activeForeshadowing.length === 0" class="empty-state">
                                <p>暂无活跃伏笔</p>
                                <button class="btn btn-primary" @click="showAddForeshadowingDialog">添加第一个伏笔</button>
                            </div>
                            <div v-else>
                                <div v-for="foreshadowing in activeForeshadowing" :key="foreshadowing.id" class="foreshadowing-card">
                                    <div class="foreshadowing-header">
                                        <div class="foreshadowing-info">
                                            <h3 class="foreshadowing-title">{{ foreshadowing.description }}</h3>
                                            <div class="foreshadowing-meta">
                                                <span class="foreshadowing-type">{{ getForeshadowingTypeText(foreshadowing.type) }}</span>
                                                <span class="foreshadowing-importance">重要性: {{ foreshadowing.importance }}/10</span>
                                                <span class="foreshadowing-distance">预期{{ foreshadowing.estimated_payoff_distance }}章后回收</span>
                                            </div>
                                        </div>
                                        <div class="foreshadowing-actions">
                                            <button class="btn btn-small btn-success" @click="markForeshadowingResolved(foreshadowing)" title="标记为已回收">
                                                ✅ 回收
                                            </button>
                                            <button class="btn-icon-small" @click="editForeshadowing(foreshadowing)" title="编辑">✏️</button>
                                            <button class="btn-icon-small" @click="deleteForeshadowing(foreshadowing)" title="删除">🗑️</button>
                                        </div>
                                    </div>
                                    <div class="foreshadowing-details">
                                        <div v-if="foreshadowing.planted_in_scene" class="foreshadowing-scene">
                                            <strong>埋设场景:</strong> {{ foreshadowing.planted_in_scene }}
                                        </div>
                                        <div v-if="foreshadowing.hints && foreshadowing.hints.length > 0" class="foreshadowing-hints">
                                            <strong>具体暗示:</strong>
                                            <ul class="hints-list">
                                                <li v-for="hint in foreshadowing.hints" :key="hint">{{ hint }}</li>
                                            </ul>
                                        </div>
                                        <div v-if="foreshadowing.relevant_characters && foreshadowing.relevant_characters.length > 0" class="foreshadowing-characters">
                                            <strong>相关角色:</strong> {{ foreshadowing.relevant_characters.join(', ') }}
                                        </div>
                                        <div v-if="foreshadowing.relevant_locations && foreshadowing.relevant_locations.length > 0" class="foreshadowing-locations">
                                            <strong>相关地点:</strong> {{ foreshadowing.relevant_locations.join(', ') }}
                                        </div>
                                        <div v-if="foreshadowing.tags && foreshadowing.tags.length > 0" class="foreshadowing-tags">
                                            <strong>标签:</strong>
                                            <span v-for="tag in foreshadowing.tags" :key="tag" class="tag">{{ tag }}</span>
                                        </div>
                                        <div v-if="foreshadowing.planted_at" class="foreshadowing-planted">
                                            <strong>埋设时间:</strong> {{ formatDate(foreshadowing.planted_at) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 已回收伏笔列表 -->
                        <div v-else-if="activeForeshadowingTab === 'resolved'" class="foreshadowing-list">
                            <div v-if="resolvedForeshadowing.length === 0" class="empty-state">
                                <p>暂无已回收的伏笔</p>
                            </div>
                            <div v-else>
                                <div v-for="foreshadowing in resolvedForeshadowing" :key="foreshadowing.id" class="foreshadowing-card resolved">
                                    <div class="foreshadowing-header">
                                        <div class="foreshadowing-info">
                                            <h3 class="foreshadowing-title">{{ foreshadowing.description }}</h3>
                                            <div class="foreshadowing-meta">
                                                <span class="foreshadowing-type">{{ getForeshadowingTypeText(foreshadowing.type) }}</span>
                                                <span class="foreshadowing-importance">重要性: {{ foreshadowing.importance }}/10</span>
                                                <span class="resolved-badge">已回收</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="foreshadowing-details">
                                        <div v-if="foreshadowing.planted_in_scene" class="foreshadowing-scene">
                                            <strong>埋设场景:</strong> {{ foreshadowing.planted_in_scene }}
                                        </div>
                                        <div v-if="foreshadowing.resolved_in_scene" class="foreshadowing-resolved-scene">
                                            <strong>回收场景:</strong> {{ foreshadowing.resolved_in_scene }}
                                        </div>
                                        <div v-if="foreshadowing.resolution_details" class="foreshadowing-resolution">
                                            <strong>回收详情:</strong> {{ foreshadowing.resolution_details }}
                                        </div>
                                        <div v-if="foreshadowing.resolved_at" class="foreshadowing-resolved">
                                            <strong>回收时间:</strong> {{ formatDate(foreshadowing.resolved_at) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 计划伏笔列表 -->
                        <div v-else-if="activeForeshadowingTab === 'planned'" class="foreshadowing-list">
                            <div v-if="plannedForeshadowing.length === 0" class="empty-state">
                                <p>暂无计划中的伏笔</p>
                            </div>
                            <div v-else>
                                <div v-for="foreshadowing in plannedForeshadowing" :key="foreshadowing.id" class="foreshadowing-card planned">
                                    <div class="foreshadowing-header">
                                        <div class="foreshadowing-info">
                                            <h3 class="foreshadowing-title">{{ foreshadowing.description }}</h3>
                                            <div class="foreshadowing-meta">
                                                <span class="foreshadowing-type">{{ getForeshadowingTypeText(foreshadowing.type) }}</span>
                                                <span class="foreshadowing-importance">重要性: {{ foreshadowing.importance }}/10</span>
                                                <span class="planned-badge">计划中</span>
                                            </div>
                                        </div>
                                        <div class="foreshadowing-actions">
                                            <button class="btn btn-small btn-primary" @click="activatePlannedForeshadowing(foreshadowing)" title="激活伏笔">
                                                🚀 激活
                                            </button>
                                            <button class="btn-icon-small" @click="editForeshadowing(foreshadowing)" title="编辑">✏️</button>
                                            <button class="btn-icon-small" @click="deleteForeshadowing(foreshadowing)" title="删除">🗑️</button>
                                        </div>
                                    </div>
                                    <div class="foreshadowing-details">
                                        <div v-if="foreshadowing.planned_scene" class="foreshadowing-planned-scene">
                                            <strong>计划场景:</strong> {{ foreshadowing.planned_scene }}
                                        </div>
                                        <div v-if="foreshadowing.implementation_notes" class="foreshadowing-notes">
                                            <strong>实施说明:</strong> {{ foreshadowing.implementation_notes }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 草稿箱视图 -->
                <div v-else-if="activeView === 'drafts'" class="view-container">
                    <div class="view-header">
                        <h2>草稿箱</h2>
                    </div>
                    <div class="view-content">
                        <div v-if="drafts.length === 0" class="empty-state">
                            <p>还没有任何草稿</p>
                            <p class="text-muted">请先在场景卡片中生成初稿</p>
                        </div>
                        <div v-else class="drafts-list">
                            <div v-for="draft in drafts" :key="draft.id" class="draft-card">
                                <div class="draft-header">
                                    <div class="draft-info">
                                        <h3 class="draft-title">{{ draft.title }}</h3>
                                        <span class="draft-meta">{{ draft.lastModified }}</span>
                                    </div>
                                    <div class="draft-actions">
                                        <button class="btn btn-primary" @click="editDraft(draft)">
                                            <span class="btn-icon">✏️</span>
                                            编辑
                                        </button>
                                        <button class="btn-icon-small" @click="deleteDraft(draft)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="draft-preview">
                                    {{ draft.preview }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 已发布视图 -->
                <div v-else-if="activeView === 'published'" class="view-container">
                    <div class="view-header">
                        <h2>已发布</h2>
                    </div>
                    <div class="view-content">
                        <div v-if="publishedChapters.length === 0" class="empty-state">
                            <p>还没有发布任何章节</p>
                            <p class="text-muted">请先编辑草稿并提交终稿</p>
                        </div>
                        <div v-else class="published-list">
                            <div v-for="chapter in publishedChapters" :key="chapter.id" class="published-card">
                                <div class="published-header">
                                    <div class="published-info">
                                        <h3 class="published-title">{{ chapter.title }}</h3>
                                        <span class="published-meta">发布于 {{ chapter.publishedAt }}</span>
                                    </div>
                                    <div class="published-actions">
                                        <button class="btn btn-secondary btn-small" @click="viewChapter(chapter)">
                                            <span class="btn-icon">👁️</span>
                                            查看
                                        </button>
                                    </div>
                                </div>
                                <div class="published-preview">
                                    {{ chapter.preview }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组织管理视图 -->
                <div v-else-if="activeView === 'organizations'" class="view-container">
                    <div class="view-header">
                        <h2>组织管理</h2>
                        <button class="btn btn-primary" @click="showCreateOrganizationDialog">
                            <span class="btn-icon">➕</span>
                            新建组织
                        </button>
                    </div>
                    <div class="view-content">
                        <div v-if="organizations.length === 0" class="empty-state">
                            <p>还没有创建任何组织</p>
                            <button class="btn btn-primary" @click="showCreateOrganizationDialog">创建第一个组织</button>
                        </div>
                        <div v-else class="organizations-grid">
                            <div v-for="organization in organizations" :key="organization.name" class="organization-card">
                                <div class="organization-header">
                                    <div class="organization-info">
                                        <h3 class="organization-name">
                                            {{ organization.name }}
                                            <span v-if="organization.ai_generated" class="ai-tag">🤖 AI生成</span>
                                        </h3>
                                        <span class="organization-type">{{ organization.type || '未分类' }}</span>
                                    </div>
                                    <div class="organization-actions">
                                        <button class="btn-icon-small" @click="editOrganization(organization)" title="编辑">✏️</button>
                                        <button class="btn-icon-small" @click="deleteOrganization(organization)" title="删除">🗑️</button>
                                    </div>
                                </div>
                                <div class="organization-details">
                                    <div class="organization-description">
                                        <strong>描述:</strong>
                                        <span v-if="organization.description">{{ organization.description }}</span>
                                        <span v-else class="text-muted">暂无描述</span>
                                    </div>
                                    <div class="organization-leader">
                                        <strong>领导者:</strong>
                                        <span v-if="organization.leader">{{ organization.leader }}</span>
                                        <span v-else class="text-muted">未设定</span>
                                    </div>
                                    <div class="organization-members" v-if="organization.members && organization.members.length > 0">
                                        <strong>成员:</strong>
                                        <span>{{ organization.members.join(', ') }}</span>
                                    </div>
                                    <div class="organization-influence" v-if="organization.influence_level">
                                        <strong>影响力:</strong>
                                        <span class="influence-level">
                                            {{ organization.influence_level }}
                                        </span>
                                    </div>
                                    <div class="organization-status" v-if="organization.status">
                                        <strong>状态:</strong>
                                        <span class="status-badge">
                                            {{ organization.status }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 世界观设定视图 -->
                <div v-else-if="activeView === 'worldbible'" class="view-container">
                    <div class="view-header">
                        <h2>世界观设定</h2>
                        <button class="btn btn-primary" @click="saveWorldBible" :disabled="!worldBibleChanged">
                            <span class="btn-icon">💾</span>
                            保存设定
                        </button>
                    </div>
                    <div class="view-content">
                        <div class="world-bible-editor">
                            <div class="editor-toolbar">
                                <button class="toolbar-btn" @click="insertWorldSection('基本设定')" title="插入基本设定">
                                    🌍 基本设定
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('历史背景')" title="插入历史背景">
                                    📜 历史背景
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('文化设定')" title="插入文化设定">
                                    🏛️ 文化设定
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('地理环境')" title="插入地理环境">
                                    🗺️ 地理环境
                                </button>
                                <button class="toolbar-btn" @click="insertWorldSection('魔法系统')" title="插入魔法系统">
                                    ✨ 魔法系统
                                </button>
                            </div>
                            <textarea
                                ref="worldBibleEditor"
                                v-model="worldBibleContent"
                                class="world-bible-textarea"
                                placeholder="在这里编写您的世界观设定..."
                                @input="onWorldBibleInput">
                            </textarea>
                            <div class="editor-status">
                                <span class="word-count">字数: {{ worldBibleWordCount }}</span>
                                <span class="save-status" :class="{ 'saved': !worldBibleChanged, 'unsaved': worldBibleChanged }">
                                    {{ worldBibleChanged ? '未保存' : '已保存' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主线大纲视图 -->
                <div v-else-if="activeView === 'outline'" class="view-container">
                    <div class="view-header">
                        <h2>主线大纲</h2>
                        <button class="btn btn-primary" @click="saveOutline" :disabled="!outlineChanged">
                            <span class="btn-icon">💾</span>
                            保存大纲
                        </button>
                    </div>
                    <div class="view-content">
                        <div class="outline-editor">
                            <div class="editor-toolbar">
                                <button class="toolbar-btn" @click="insertOutlineSection('故事概述')" title="插入故事概述">
                                    📖 故事概述
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('主要角色')" title="插入主要角色">
                                    👥 主要角色
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('情节结构')" title="插入情节结构">
                                    🎭 情节结构
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('主题思想')" title="插入主题思想">
                                    💭 主题思想
                                </button>
                                <button class="toolbar-btn" @click="insertOutlineSection('分章计划')" title="插入分章计划">
                                    📑 分章计划
                                </button>
                            </div>
                            <textarea
                                ref="outlineEditor"
                                v-model="outlineContent"
                                class="outline-textarea"
                                placeholder="在这里编写您的主线大纲..."
                                @input="onOutlineInput">
                            </textarea>
                            <div class="editor-status">
                                <span class="word-count">字数: {{ outlineWordCount }}</span>
                                <span class="save-status" :class="{ 'saved': !outlineChanged, 'unsaved': outlineChanged }">
                                    {{ outlineChanged ? '未保存' : '已保存' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他视图的占位符 -->
                <div v-else class="view-container">
                    <div class="view-header">
                        <h2>{{ getViewTitle() }}</h2>
                    </div>
                    <div class="view-content">
                        <p class="placeholder-text">{{ activeView }} 功能正在开发中...</p>
                    </div>
                </div>
            </main>
        </div>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-left">
                <span v-if="currentProject" class="status-item">
                    项目: {{ currentProject.name }}
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">就绪</span>
            </div>
        </footer>

        <!-- 角色编辑对话框 -->
        <div v-if="showCharacterDialog" class="modal-overlay">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingCharacter ? '编辑角色' : '新建角色' }}</h3>
                    <button class="modal-close" @click="closeCharacterDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveCharacter">
                        <div class="form-group">
                            <label>角色名称 *</label>
                            <input type="text" v-model="characterForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>角色描述</label>
                            <input type="text" v-model="characterForm.description" placeholder="如：不情愿的英雄">
                        </div>
                        <div class="form-group">
                            <label>动机</label>
                            <textarea v-model="characterForm.motivation" placeholder="角色的主要动机和目标"></textarea>
                        </div>
                        <div class="form-group">
                            <label>外貌描述</label>
                            <textarea v-model="characterForm.appearance" placeholder="角色的外貌特征"></textarea>
                        </div>
                        <div class="form-group">
                            <label>初始情绪</label>
                            <input type="text" v-model="characterForm.emotion" placeholder="如：焦虑、兴奋、平静">
                        </div>
                        <div class="form-group">
                            <label>初始位置</label>
                            <select v-model="characterForm.location">
                                <option value="">选择位置</option>
                                <option v-for="location in locations" :key="location.name" :value="location.name">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>当前状态</label>
                            <textarea v-model="characterForm.current_status" placeholder="角色的当前状态描述，如：正在寻找线索、准备战斗、休息中等"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeCharacterDialog">取消</button>
                    <button class="btn btn-primary" @click="saveCharacter">保存</button>
                </div>
            </div>
        </div>

        <!-- 地点编辑对话框 -->
        <div v-if="showLocationDialog" class="modal-overlay">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingLocation ? '编辑地点' : '新建地点' }}</h3>
                    <button class="modal-close" @click="closeLocationDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveLocation">
                        <div class="form-group">
                            <label>地点名称 *</label>
                            <input type="text" v-model="locationForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>地点类型</label>
                            <input type="text" v-model="locationForm.type" placeholder="例如：城市、建筑、自然环境、室内场所等">
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea v-model="locationForm.description" placeholder="地点的详细描述"></textarea>
                        </div>
                        <div class="form-group">
                            <label>氛围</label>
                            <input type="text" v-model="locationForm.atmosphere" placeholder="如：神秘、温馨、紧张">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeLocationDialog">取消</button>
                    <button class="btn btn-primary" @click="saveLocation">保存</button>
                </div>
            </div>
        </div>

        <!-- 组织编辑对话框 -->
        <div v-if="showOrganizationDialog" class="modal-overlay">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingOrganization ? '编辑组织' : '新建组织' }}</h3>
                    <button class="modal-close" @click="closeOrganizationDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveOrganization">
                        <div class="form-group">
                            <label>组织名称 *</label>
                            <input type="text" v-model="organizationForm.name" required>
                        </div>
                        <div class="form-group">
                            <label>组织类型</label>
                            <select v-model="organizationForm.type">
                                <option value="">选择类型</option>
                                <option value="政府">政府机构</option>
                                <option value="军事">军事组织</option>
                                <option value="商业">商业团体</option>
                                <option value="宗教">宗教组织</option>
                                <option value="学术">学术机构</option>
                                <option value="秘密">秘密组织</option>
                                <option value="犯罪">犯罪集团</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织描述</label>
                            <textarea v-model="organizationForm.description" placeholder="组织的详细描述、宗旨、历史等"></textarea>
                        </div>
                        <div class="form-group">
                            <label>领导者</label>
                            <select v-model="organizationForm.leader">
                                <option value="">选择领导者</option>
                                <option v-for="character in characters" :key="character.id" :value="character.name">
                                    {{ character.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织成员</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="character.id" class="checkbox-label">
                                    <input type="checkbox" :value="character.name" v-model="organizationForm.members">
                                    {{ character.name }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>影响力等级</label>
                            <input type="text" v-model="organizationForm.influence_level" placeholder="例如：地方性、区域性、国家级、国际性、传奇级等">
                        </div>
                        <div class="form-group">
                            <label>组织状态</label>
                            <input type="text" v-model="organizationForm.status" placeholder="例如：活跃、休眠、衰落、崛起、解散、隐秘等">
                        </div>
                        <div class="form-group">
                            <label>总部位置</label>
                            <select v-model="organizationForm.headquarters">
                                <option value="">选择总部</option>
                                <option v-for="location in locations" :key="location.name" :value="location.name">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>组织资源</label>
                            <textarea v-model="organizationForm.resources" placeholder="组织拥有的资源、财富、装备等"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeOrganizationDialog">取消</button>
                    <button class="btn btn-primary" @click="saveOrganization">保存</button>
                </div>
            </div>
        </div>

        <!-- 场景编辑对话框 -->
        <div v-if="showSceneDialog" class="modal-overlay">
            <div class="modal-dialog modal-large" @click.stop>
                <div class="modal-header">
                    <h3>{{ editingScene ? '编辑场景' : '新建场景' }}</h3>
                    <div class="modal-actions" v-if="!editingScene">
                        <button type="button" class="btn btn-secondary" @click="showAiCreateSceneDialog" :disabled="!apiConfigured || isGenerating">
                            <span class="btn-icon">🤖</span>
                            AI创建
                        </button>
                    </div>
                    <button class="modal-close" @click="closeSceneDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveScene">
                        <div class="form-group">
                            <label>场景ID *</label>
                            <input type="text" v-model="sceneForm.scene_id" required
                                   placeholder="如：sc_001_introduction">
                        </div>
                        <div class="form-group">
                            <label>故事线</label>
                            <select v-model="sceneForm.storyline">
                                <option value="main">主线</option>
                                <option value="side_quest_A">支线A</option>
                                <option value="side_quest_B">支线B</option>
                                <option value="flashback">回忆</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>参与角色</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="character.name" class="checkbox-label">
                                    <input type="checkbox" :value="character.name" v-model="sceneForm.characters">
                                    {{ character.name }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>场景地点</label>
                            <select v-model="sceneForm.location">
                                <option value="">选择地点</option>
                                <option v-for="location in locations" :key="location.name" :value="location.name">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>场景目标</label>
                            <textarea v-model="sceneForm.goals" placeholder="描述这个场景要达成的目标，例如：推进主线剧情、展现角色性格、建立世界观等"></textarea>
                        </div>
                        <div class="form-group">
                            <label>场景简述</label>
                            <textarea v-model="sceneForm.description" placeholder="场景的简要描述"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeSceneDialog">取消</button>
                    <button class="btn btn-primary" @click="saveScene">保存</button>
                </div>
            </div>
        </div>

        <!-- API配置对话框 -->
        <div v-if="showApiDialog" class="modal-overlay">
            <div class="modal-dialog modal-large" @click.stop>
                <div class="modal-header">
                    <h3>AI API 配置</h3>
                    <button class="modal-close" @click="closeApiDialog">×</button>
                </div>
                <div class="modal-body">
                    <form @submit.prevent="saveApiConfig">
                        <div class="api-config-tabs">
                            <div class="tab-header">
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'primary_generation'}" @click="activeApiTab = 'primary_generation'">
                                    主要生成API
                                </button>
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'analysis'}" @click="activeApiTab = 'analysis'">
                                    分析API
                                </button>
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'secondary_generation'}" @click="activeApiTab = 'secondary_generation'">
                                    辅助生成API
                                </button>
                                <button type="button" class="tab-button" :class="{active: activeApiTab === 'planning'}" @click="activeApiTab = 'planning'">
                                    规划API
                                </button>
                            </div>

                            <!-- 动态API配置标签页 -->
                            <div v-for="(apiInfo, apiType) in apiTypes" :key="apiType" v-show="activeApiTab === apiType" class="tab-content">
                                <div class="api-config-header">
                                    <h4>{{ apiInfo.title }}</h4>
                                    <div class="api-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" v-model="aiApisForm[apiType].enabled">
                                            <span class="toggle-slider"></span>
                                        </label>
                                        <span class="toggle-label">{{ aiApisForm[apiType].enabled ? '已启用' : '已禁用' }}</span>
                                    </div>
                                </div>
                                <p class="tab-description">{{ apiInfo.description }}</p>

                                <div v-if="aiApisForm[apiType].enabled" class="api-form-content">
                                    <div class="form-group">
                                        <label>API Base URL</label>
                                        <input type="url" v-model="aiApisForm[apiType].base_url"
                                               placeholder="https://api.openai.com/v1">
                                        <small class="form-help">API服务器地址，留空使用默认OpenAI地址</small>
                                    </div>
                                    <div class="form-group">
                                        <label>API Key</label>
                                        <input type="password" v-model="aiApisForm[apiType].api_key"
                                               placeholder="sk-... 或其他API密钥">
                                        <small class="form-help">请输入您的API密钥</small>
                                    </div>
                                    <div class="form-group">
                                        <label>模型名称</label>
                                        <input type="text" v-model="aiApisForm[apiType].model"
                                               :placeholder="apiInfo.defaultModel">
                                        <small class="form-help">可以是OpenAI模型或其他兼容模型</small>
                                    </div>
                                    <div class="form-group">
                                        <label>常用模型快选</label>
                                        <div class="model-buttons">
                                            <button type="button" class="btn btn-small" @click="setApiModel(apiType, 'gpt-3.5-turbo')">GPT-3.5 Turbo</button>
                                            <button type="button" class="btn btn-small" @click="setApiModel(apiType, 'gpt-4')">GPT-4</button>
                                            <button type="button" class="btn btn-small" @click="setApiModel(apiType, 'gpt-4-turbo')">GPT-4 Turbo</button>
                                            <button type="button" class="btn btn-small" @click="setApiModel(apiType, 'claude-3-sonnet')">Claude-3 Sonnet</button>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Temperature (创造性)</label>
                                        <input type="range" v-model="aiApisForm[apiType].temperature" min="0" max="1" step="0.1">
                                        <div class="range-value">{{ aiApisForm[apiType].temperature }}</div>
                                        <small class="form-help">{{ apiInfo.temperatureHelp }}</small>
                                    </div>
                                </div>
                                <div v-else class="api-disabled-notice">
                                    <p>此API配置已禁用。启用后可以配置相关参数。</p>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <p class="form-note">
                                <strong>提示：</strong>您可以为不同功能配置不同的API，也可以使用相同的API配置。
                                至少需要启用并配置一个API的密钥才能使用AI功能。建议启用"主要生成API"和"分析API"。
                            </p>
                            <div class="api-status-summary">
                                <h5>当前配置状态：</h5>
                                <div class="status-grid">
                                    <div v-for="(apiInfo, apiType) in apiTypes" :key="apiType" class="status-item">
                                        <span class="status-name">{{ apiInfo.title }}:</span>
                                        <span :class="['status-badge', getApiStatus(apiType)]">
                                            {{ getApiStatusText(apiType) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeApiDialog">取消</button>
                    <button class="btn btn-primary" @click="saveApiConfig">保存</button>
                </div>
            </div>
        </div>

        <!-- 文本编辑器对话框 -->
        <div v-if="showEditorDialog" class="modal-overlay editor-overlay">
            <div class="modal-dialog editor-dialog" @click.stop>
                <div class="modal-header">
                    <h3>编辑草稿: {{ editingDraft?.title }}</h3>
                    <div class="editor-actions">
                        <button class="btn btn-success" @click="publishDraft" :disabled="!editorContent.trim()">
                            <span class="btn-icon">📤</span>
                            完成并提交
                        </button>
                        <button class="btn btn-secondary" @click="saveDraft">
                            <span class="btn-icon">💾</span>
                            保存草稿
                        </button>
                        <button class="modal-close" @click="closeEditorDialog">×</button>
                    </div>
                </div>
                <div class="modal-body editor-body">
                    <div class="editor-toolbar">
                        <button class="toolbar-btn" @click="formatText('bold')" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button class="toolbar-btn" @click="formatText('italic')" title="斜体">
                            <em>I</em>
                        </button>
                        <button class="toolbar-btn" @click="formatText('underline')" title="下划线">
                            <u>U</u>
                        </button>
                        <div class="toolbar-separator"></div>
                        <button class="toolbar-btn" @click="insertText('dialogue')" title="插入对话">
                            💬
                        </button>
                        <button class="toolbar-btn" @click="insertText('description')" title="插入描述">
                            📝
                        </button>
                        <button class="toolbar-btn" @click="insertText('action')" title="插入动作">
                            🎬
                        </button>
                    </div>
                    <textarea
                        ref="editor"
                        v-model="editorContent"
                        class="text-editor"
                        placeholder="在这里编辑您的故事..."
                        @input="onEditorInput"
                        @keydown="onEditorKeydown">
                    </textarea>
                    <div class="editor-status">
                        <span class="word-count">字数: {{ wordCount }}</span>
                        <span class="save-status" :class="{ 'saved': isSaved, 'unsaved': !isSaved }">
                            {{ isSaved ? '已保存' : '未保存' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI生成状态显示 -->
        <div v-if="isGenerating" class="generating-overlay">
            <div class="generating-dialog">
                <div class="generating-spinner"></div>
                <div class="generating-text">{{ generatingText }}</div>
                <div class="generating-subtext">{{ generatingSubtext }}</div>
            </div>
        </div>

        <!-- 通知消息 -->
        <div v-if="notification.show" class="notification" :class="notification.type">
            <div class="notification-content">
                <span class="notification-icon">{{ notification.icon }}</span>
                <span class="notification-message">{{ notification.message }}</span>
                <button class="notification-close" @click="hideNotification">×</button>
            </div>
        </div>

        <!-- AI创建场景参数对话框 -->
        <div v-if="showAiSceneDialog" class="modal-overlay">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>AI创建场景</h3>
                    <button class="modal-close" @click="closeAiSceneDialog">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-description">请指定场景参数（可选择性填写，留空则由AI自动生成）：</p>
                    <form @submit.prevent="aiCreateSceneWithParams">
                        <div class="form-group">
                            <label>场景ID</label>
                            <input type="text" v-model="aiSceneForm.scene_id"
                                   placeholder="如：sc_001_introduction（留空则AI自动生成）">
                        </div>
                        <div class="form-group">
                            <label>参与角色</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="character.name" class="checkbox-label">
                                    <input type="checkbox" :value="character.name" v-model="aiSceneForm.characters">
                                    {{ character.name }}
                                </label>
                            </div>
                            <small class="form-hint">不选择则由AI根据剧情需要自动选择角色</small>
                        </div>
                        <div class="form-group">
                            <label>排除角色</label>
                            <div class="checkbox-group">
                                <label v-for="character in characters" :key="'exclude-' + character.name" class="checkbox-label">
                                    <input type="checkbox" :value="character.name" v-model="aiSceneForm.excludeCharacters">
                                    {{ character.name }}
                                </label>
                            </div>
                            <small class="form-hint">选中的角色将不会出现在AI创建场景的上下文中，也不会被AI考虑</small>
                        </div>
                        <div class="form-group">
                            <label>场景地点</label>
                            <select v-model="aiSceneForm.location">
                                <option value="">由AI自动选择</option>
                                <option v-for="location in locations" :key="location.name" :value="location.name">
                                    {{ location.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>场景目标</label>
                            <textarea v-model="aiSceneForm.goals"
                                      placeholder="描述这个场景要达成的目标（留空则AI自动设定）"></textarea>
                        </div>
                        <div class="form-group">
                            <label>场景简述</label>
                            <textarea v-model="aiSceneForm.description"
                                      placeholder="场景的简要描述（留空则AI自动生成）"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeAiSceneDialog">取消</button>
                    <button class="btn btn-primary" @click="aiCreateSceneWithParams" :disabled="isGenerating">
                        <span class="btn-icon">🤖</span>
                        {{ isGenerating ? 'AI创建中...' : '开始创建' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 工作流程配置对话框 -->
        <div v-if="showWorkflowDialog" class="modal-overlay">
            <div class="modal-dialog modal-large" @click.stop>
                <div class="modal-header">
                    <h3>全自动化创作流程配置</h3>
                    <button class="modal-close" @click="closeWorkflowDialog">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-description">配置您的全自动化创作流程参数：</p>
                    <form @submit.prevent="startCompleteWorkflow">
                        <div class="form-group">
                            <label>小说类型</label>
                            <input type="text" v-model="workflowForm.genre" placeholder="例如：都市异能、玄幻修仙、科幻、悬疑推理等">
                        </div>
                        <div class="form-group">
                            <label>主题设定</label>
                            <input type="text" v-model="workflowForm.theme" placeholder="例如：成长蜕变、复仇、爱情、友情、权力斗争等">
                        </div>
                        <div class="form-group">
                            <label>写作风格</label>
                            <select v-model="workflowForm.style">
                                <option value="现代网文风格">现代网文风格</option>
                                <option value="传统文学风格">传统文学风格</option>
                                <option value="轻小说风格">轻小说风格</option>
                                <option value="古典文学风格">古典文学风格</option>
                                <option value="现代商业小说风格">现代商业小说风格</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>特殊要求</label>
                            <textarea v-model="workflowForm.special_requirements" 
                                      placeholder="任何特殊的创作要求，例如：必须包含的情节元素、禁忌内容、特定的叙述方式等"></textarea>
                        </div>
                        
                        <h4>创作规模配置</h4>
                        <div class="form-group">
                            <label>目标总字数</label>
                            <input type="number" v-model="workflowForm.target_total_words" min="10000" max="10000000">
                            <small class="form-help">建议：短篇5-10万字，中篇20-50万字，长篇100-300万字</small>
                        </div>
                        <div class="form-group">
                            <label>平均章节字数</label>
                            <input type="number" v-model="workflowForm.average_chapter_words" min="500" max="10000">
                            <small class="form-help">建议：1500-3000字/章</small>
                        </div>
                        <div class="form-group">
                            <label>目标分卷数</label>
                            <input type="number" v-model="workflowForm.target_volumes" min="1" max="20">
                            <small class="form-help">系统将自动分配章节到各分卷</small>
                        </div>
                        
                        <div class="workflow-calculation">
                            <h4>规模预览</h4>
                            <div class="calculation-grid">
                                <div class="calc-item">
                                    <span class="calc-label">预估总章节数：</span>
                                    <span class="calc-value">{{ getEstimatedChapters() }}</span>
                                </div>
                                <div class="calc-item">
                                    <span class="calc-label">平均每卷章节数：</span>
                                    <span class="calc-value">{{ getChaptersPerVolume() }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <h4>创作控制</h4>
                        <div class="form-group">
                            <label>每批次章节数</label>
                            <input type="number" v-model="workflowForm.chapters_per_batch" min="1" max="20">
                            <small class="form-help">一次性生成的章节数量，建议3-10章</small>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" v-model="workflowForm.pause_between_volumes">
                                分卷间暂停确认
                            </label>
                            <small class="form-help">每完成一卷后等待用户确认再继续下一卷</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeWorkflowDialog">取消</button>
                    <button class="btn btn-primary" @click="startCompleteWorkflow" :disabled="isGenerating">
                        <span class="btn-icon">🚀</span>
                        {{ isGenerating ? '启动中...' : '启动创作流程' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 大纲审阅对话框 -->
        <div v-if="showOutlineReviewDialog" class="modal-overlay">
            <div class="modal-dialog modal-xlarge" @click.stop>
                <div class="modal-header">
                    <h3>主线大纲审阅</h3>
                    <div class="modal-actions">
                        <button class="btn btn-secondary btn-small" @click="toggleOutlineEditMode">
                            {{ outlineEditMode ? '取消编辑' : '编辑大纲' }}
                        </button>
                    </div>
                    <button class="modal-close" @click="closeOutlineReviewDialog">×</button>
                </div>
                <div class="modal-body">
                    <div v-if="!outlineEditMode" class="outline-preview">
                        <pre class="outline-content">{{ editedOutlineContent }}</pre>
                    </div>
                    <div v-else class="outline-editor">
                        <textarea v-model="editedOutlineContent" 
                                  class="outline-textarea"
                                  placeholder="编辑主线大纲内容..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeOutlineReviewDialog">暂时关闭</button>
                    <button class="btn btn-primary" @click="confirmMainOutline" :disabled="isGenerating">
                        <span class="btn-icon">✓</span>
                        {{ isGenerating ? '处理中...' : '确认大纲，生成细纲' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 分卷细纲审阅对话框 -->
        <div v-if="showVolumeReviewDialog" class="modal-overlay">
            <div class="modal-dialog modal-xlarge" @click.stop>
                <div class="modal-header">
                    <h3>分卷细纲审阅</h3>
                    <button class="modal-close" @click="closeVolumeReviewDialog">×</button>
                </div>
                <div class="modal-body">
                    <div class="volume-outline-tabs">
                        <div v-for="volume in Object.values(editedVolumeOutlines)" :key="volume.id" class="volume-tab">
                            <div class="volume-tab-header">
                                <h4>{{ volume.title }}</h4>
                                <button class="btn btn-secondary btn-small" @click="toggleVolumeEditMode(volume.id)">
                                    {{ volume.editMode ? '取消编辑' : '编辑此卷' }}
                                </button>
                            </div>
                            <div class="volume-tab-content">
                                <div v-if="!volume.editMode" class="volume-preview">
                                    <pre class="volume-content">{{ volume.content }}</pre>
                                </div>
                                <div v-else class="volume-editor">
                                    <textarea v-model="volume.content" 
                                              class="volume-textarea"
                                              :placeholder="`编辑${volume.title}细纲内容...`"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeVolumeReviewDialog">暂时关闭</button>
                    <button class="btn btn-primary" @click="confirmVolumeOutlines" :disabled="isGenerating">
                        <span class="btn-icon">✓</span>
                        {{ isGenerating ? '处理中...' : '确认细纲，准备创作' }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 分卷创作确认对话框 -->
        <div v-if="showVolumeWritingDialog" class="modal-overlay">
            <div class="modal-dialog" @click.stop>
                <div class="modal-header">
                    <h3>确认开始分卷创作</h3>
                    <button class="modal-close" @click="closeVolumeWritingDialog">×</button>
                </div>
                <div class="modal-body">
                    <p class="modal-description">您确定要开始第{{ selectedVolume?.replace('vol_', '') }}卷的创作吗？</p>
                    <div class="workflow-config">
                        <h4>创作参数</h4>
                        <div class="config-item">
                            <span class="config-label">每批次章节数：</span>
                            <span class="config-value">{{ workflowForm.chapters_per_batch }}</span>
                        </div>
                        <div class="config-item">
                            <span class="config-label">每章字数：</span>
                            <span class="config-value">{{ workflowForm.average_chapter_words }}</span>
                        </div>
                    </div>
                    <div class="workflow-warning">
                        <p><strong>注意：</strong>分卷创作可能需要较长时间，请确保：</p>
                        <ul>
                            <li>网络连接稳定</li>
                            <li>API额度充足</li>
                            <li>不要关闭程序</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" @click="closeVolumeWritingDialog">取消</button>
                    <button class="btn btn-primary" @click="confirmVolumeWriting" :disabled="isGenerating">
                        <span class="btn-icon">🚀</span>
                        {{ isGenerating ? '创作中...' : '开始创作' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 伏笔添加/编辑对话框 -->
    <div v-if="showForeshadowingDialog" class="modal-overlay" @click.self="closeForeshadowingDialog">
        <div class="modal">
            <div class="modal-header">
                <h3>{{ editingForeshadowing ? '编辑伏笔' : '添加伏笔' }}</h3>
                <button class="modal-close" @click="closeForeshadowingDialog">×</button>
            </div>
            <div class="modal-body">
                <form @submit.prevent="saveForeshadowing">
                    <div class="form-group">
                        <label>伏笔类型</label>
                        <select v-model="foreshadowingForm.type" required>
                            <option value="character_secret">角色秘密</option>
                            <option value="item_clue">物品线索</option>
                            <option value="event_foreshadowing">事件预示</option>
                            <option value="ability_hint">能力暗示</option>
                            <option value="relationship_clue">关系线索</option>
                            <option value="world_expansion">世界观扩展</option>
                            <option value="plot_twist_setup">情节转折预示</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>伏笔描述</label>
                        <textarea v-model="foreshadowingForm.description"
                                  placeholder="简要描述这个伏笔的内容和意义"
                                  required rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>具体暗示（每行一个）</label>
                        <textarea v-model="hintsText"
                                  placeholder="文本中的具体暗示内容，每行一个"
                                  rows="4"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>重要程度 (1-10)</label>
                            <input type="number" v-model.number="foreshadowingForm.importance"
                                   min="1" max="10" required>
                        </div>
                        <div class="form-group">
                            <label>预期回收距离（章数）</label>
                            <input type="number" v-model.number="foreshadowingForm.estimated_payoff_distance"
                                   min="1" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>埋设场景</label>
                        <input type="text" v-model="foreshadowingForm.planted_in_scene"
                               placeholder="在哪个场景中埋设这个伏笔">
                    </div>
                    <div class="form-group">
                        <label>相关角色（逗号分隔）</label>
                        <input type="text" v-model="charactersText"
                               placeholder="与此伏笔相关的角色名称">
                    </div>
                    <div class="form-group">
                        <label>相关地点（逗号分隔）</label>
                        <input type="text" v-model="locationsText"
                               placeholder="与此伏笔相关的地点名称">
                    </div>
                    <div class="form-group">
                        <label>标签（逗号分隔）</label>
                        <input type="text" v-model="tagsText"
                               placeholder="用于分类和搜索的标签">
                    </div>
                    <div class="form-group">
                        <label>实施说明</label>
                        <textarea v-model="foreshadowingForm.implementation_notes"
                                  placeholder="如何在场景中自然地埋设这个伏笔"
                                  rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" @click="closeForeshadowingDialog">取消</button>
                <button class="btn btn-primary" @click="saveForeshadowing">
                    <span class="btn-icon">💾</span>
                    {{ editingForeshadowing ? '更新' : '添加' }}
                </button>
            </div>
        </div>
    </div>

    <!-- 伏笔回收对话框 -->
    <div v-if="showForeshadowingResolveDialog" class="modal-overlay" @click.self="closeForeshadowingResolveDialog">
        <div class="modal">
            <div class="modal-header">
                <h3>标记伏笔回收</h3>
                <button class="modal-close" @click="closeForeshadowingResolveDialog">×</button>
            </div>
            <div class="modal-body">
                <div v-if="resolvingForeshadowing" class="foreshadowing-info">
                    <h4>{{ resolvingForeshadowing.description }}</h4>
                    <p class="text-muted">{{ getForeshadowingTypeText(resolvingForeshadowing.type) }} | 重要性: {{ resolvingForeshadowing.importance }}/10</p>
                </div>
                <form @submit.prevent="saveForeshadowingResolution">
                    <div class="form-group">
                        <label>回收场景</label>
                        <input type="text" v-model="foreshadowingResolveForm.resolved_in_scene"
                               placeholder="在哪个场景中回收了这个伏笔" required>
                    </div>
                    <div class="form-group">
                        <label>回收详情</label>
                        <textarea v-model="foreshadowingResolveForm.resolution_details"
                                  placeholder="描述伏笔是如何被回收的，产生了什么效果"
                                  rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" @click="closeForeshadowingResolveDialog">取消</button>
                <button class="btn btn-success" @click="saveForeshadowingResolution">
                    <span class="btn-icon">✅</span>
                    标记为已回收
                </button>
            </div>
        </div>
    </div>

    <script src="scripts/app.js"></script>
</body>
</html>
